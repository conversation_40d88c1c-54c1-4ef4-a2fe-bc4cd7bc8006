using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("GSTRates")]
[DisplayName("GST Rates"), InstanceName("Gst Rates"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[LookupScript]
[UniqueConstraint(new[] { "IGSTPercent", "CGSTPercent", "SGSTPercent" })]
public sealed partial class GstRatesRow : Row<GstRatesRow.RowFields>, IIdRow, INameRow,IRowNumberedRow
{
    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }
    [DisplayName("GST Rate Id"), Column("GSTRateId"), Identity, IdProperty,Hidden]
    public int? GSTRateId { get => fields.GSTRateId[this]; set => fields.GSTRateId[this] = value; }

    [DisplayName("IGST %"), Column("IGSTPercent"), Size(18), Scale(2), NotNull, LookupInclude]
    public decimal? IGSTPercent { get => fields.IGSTPercent[this]; set => fields.IGSTPercent[this] = value; }

    [DisplayName("IGST Cess%"), Column("IGSTCessPercent"), Size(18), Scale(2)]
    public decimal? IGSTCessPercent { get => fields.IGSTCessPercent[this]; set => fields.IGSTCessPercent[this] = value; }

    [DisplayName("CGST %"), Column("CGSTPercent"), Size(18), Scale(2), NotNull, LookupInclude]
    public decimal? CGSTPercent { get => fields.CGSTPercent[this]; set => fields.CGSTPercent[this] = value; }

    [DisplayName("CGST Cess%"), Column("CGSTCessPercent"), Size(18), Scale(2)]
    public decimal? CGSTCessPercent { get => fields.CGSTCessPercent[this]; set => fields.CGSTCessPercent[this] = value; }

    [DisplayName("SGST %"), Column("SGSTPercent"), Size(18), Scale(2), NotNull, LookupInclude]
    public decimal? SGSTPercent { get => fields.SGSTPercent[this]; set => fields.SGSTPercent[this] = value; }

    [DisplayName("SGST Cess%"), Column("SGSTCessPercent"), Size(18), Scale(2)]
    public decimal? SGSTCessPercent { get => fields.SGSTCessPercent[this]; set => fields.SGSTCessPercent[this] = value; }

    [DisplayName("w.e.f Date"), Column("wefDate"), NotNull]
    public DateTime? WefDate { get => fields.WefDate[this]; set => fields.WefDate[this] = value; }

    [DisplayName("Is Default"), NotNull, LookupInclude]
    public bool? Current { get => fields.Current[this]; set => fields.Current[this] = value; }

    [DisplayName("Remarks"), Size(500), QuickSearch, NameProperty]
    public string Remarks { get => fields.Remarks[this]; set => fields.Remarks[this] = value; }
}