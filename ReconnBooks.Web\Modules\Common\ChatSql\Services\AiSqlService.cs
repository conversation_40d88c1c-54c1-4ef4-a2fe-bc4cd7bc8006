using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OpenAI;
using OpenAI.Chat;
using ReconnBooks.Common.ChatSql.Models;

namespace ReconnBooks.Common.ChatSql.Services
{
    public interface IAiSqlService
    {
        Task<string> ConvertToSqlAsync(string naturalLanguageQuery, string databaseSchema, ConversationContext? context = null);
        Task<string> ExplainQueryAsync(string sqlQuery, string databaseSchema);
        Task<bool> IsServiceAvailableAsync();
    }

    public class AiSqlService : IAiSqlService
    {
        private readonly IOptions<ChatSqlConfiguration> _config;
        private readonly ILogger<AiSqlService> _logger;
        private readonly OpenAIClient? _openAiClient;

        public AiSqlService(IOptions<ChatSqlConfiguration> config, ILogger<AiSqlService> logger)
        {
            _config = config;
            _logger = logger;

            try
            {
                if (_config.Value.Provider.Equals("OpenAI", StringComparison.OrdinalIgnoreCase) &&
                    !string.IsNullOrEmpty(_config.Value.OpenAI.ApiKey))
                {
                    _openAiClient = new OpenAIClient(_config.Value.OpenAI.ApiKey);
                }
                else if (_config.Value.Provider.Equals("AzureOpenAI", StringComparison.OrdinalIgnoreCase) &&
                         !string.IsNullOrEmpty(_config.Value.AzureOpenAI.ApiKey))
                {
                    _openAiClient = new OpenAIClient(
                        _config.Value.AzureOpenAI.ApiKey,
                        new OpenAIClientOptions
                        {
                            Endpoint = new Uri(_config.Value.AzureOpenAI.Endpoint)
                        });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize AI client");
            }
        }

        public async Task<bool> IsServiceAvailableAsync()
        {
            return _openAiClient != null &&
                   (!string.IsNullOrEmpty(_config.Value.OpenAI.ApiKey) ||
                    !string.IsNullOrEmpty(_config.Value.AzureOpenAI.ApiKey));
        }

        public async Task<string> ConvertToSqlAsync(string naturalLanguageQuery, string databaseSchema, ConversationContext? context = null)
        {
            if (_openAiClient == null)
            {
                throw new InvalidOperationException("AI service is not properly configured");
            }

            try
            {
                var systemPrompt = BuildSystemPrompt(databaseSchema);
                var userPrompt = BuildUserPrompt(naturalLanguageQuery, context);

                var messages = new List<ChatMessage>
                {
                    ChatMessage.CreateSystemMessage(systemPrompt),
                    ChatMessage.CreateUserMessage(userPrompt)
                };

                // Add conversation context if available
                if (context?.Messages?.Any() == true)
                {
                    foreach (var msg in context.Messages.TakeLast(5)) // Limit context to last 5 messages
                    {
                        if (msg.Role == "user")
                        {
                            messages.Add(ChatMessage.CreateUserMessage(msg.Content));
                        }
                        else if (msg.Role == "assistant" && !string.IsNullOrEmpty(msg.SqlQuery))
                        {
                            messages.Add(ChatMessage.CreateAssistantMessage($"Generated SQL: {msg.SqlQuery}"));
                        }
                    }
                }

                var chatRequest = new ChatCompletionOptions
                {
                    Model = GetModelName(),
                    Messages = messages,
                    MaxTokens = _config.Value.OpenAI.MaxTokens,
                    Temperature = _config.Value.OpenAI.Temperature
                };

                var response = await _openAiClient.GetChatCompletionsAsync(chatRequest);
                var sqlQuery = ExtractSqlFromResponse(response.Value.Content[0].Text);

                _logger.LogInformation("Successfully converted natural language to SQL. Query length: {Length}", sqlQuery.Length);
                return sqlQuery;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting natural language to SQL: {Query}", naturalLanguageQuery);
                throw new InvalidOperationException($"Failed to convert query to SQL: {ex.Message}", ex);
            }
        }

        public async Task<string> ExplainQueryAsync(string sqlQuery, string databaseSchema)
        {
            if (_openAiClient == null)
            {
                throw new InvalidOperationException("AI service is not properly configured");
            }

            try
            {
                var systemPrompt = $@"
You are a SQL expert. Explain the given SQL query in simple, non-technical terms.
Focus on what data the query retrieves and how it's organized.

Database Schema:
{databaseSchema}

Instructions:
1. Explain what the query does in plain English
2. Mention which tables are involved
3. Explain any joins, filters, or grouping
4. Keep the explanation concise and user-friendly
";

                var userPrompt = $"Please explain this SQL query:\n\n{sqlQuery}";

                var messages = new List<ChatMessage>
                {
                    ChatMessage.CreateSystemMessage(systemPrompt),
                    ChatMessage.CreateUserMessage(userPrompt)
                };

                var chatRequest = new ChatCompletionOptions
                {
                    Model = GetModelName(),
                    Messages = messages,
                    MaxTokens = 500,
                    Temperature = 0.3f
                };

                var response = await _openAiClient.GetChatCompletionsAsync(chatRequest);
                return response.Value.Content[0].Text;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error explaining SQL query");
                return "Unable to explain the query at this time.";
            }
        }

        private string BuildSystemPrompt(string databaseSchema)
        {
            return $@"
You are an expert SQL query generator for Microsoft SQL Server. Your task is to convert natural language questions into accurate SQL queries.

Database Schema:
{databaseSchema}

Instructions:
1. Generate ONLY valid SQL SELECT statements
2. Use proper SQL Server syntax and functions
3. Always use table aliases for better readability
4. Include appropriate WHERE clauses for filtering
5. Use JOINs when querying multiple tables
6. Add ORDER BY clauses when sorting is implied
7. Use aggregate functions (COUNT, SUM, AVG, etc.) when appropriate
8. Limit results to reasonable numbers (use TOP clause if needed)
9. Handle date comparisons properly using SQL Server date functions
10. Use LIKE operator with wildcards for text searches
11. Be case-insensitive in text comparisons
12. Return only the SQL query without any explanation or formatting

Important Security Rules:
- NEVER generate INSERT, UPDATE, DELETE, DROP, ALTER, CREATE, or TRUNCATE statements
- NEVER use EXEC or EXECUTE statements
- Only generate SELECT queries
- Do not include any comments in the SQL

If the request cannot be fulfilled with a SELECT statement, respond with: 'INVALID_REQUEST'
";
        }

        private string BuildUserPrompt(string naturalLanguageQuery, ConversationContext? context)
        {
            var prompt = new StringBuilder();
            prompt.AppendLine($"Convert this natural language question to SQL: {naturalLanguageQuery}");

            if (context?.Messages?.Any() == true)
            {
                prompt.AppendLine("\nPrevious conversation context:");
                foreach (var msg in context.Messages.TakeLast(3))
                {
                    if (msg.Role == "user")
                    {
                        prompt.AppendLine($"User asked: {msg.Content}");
                    }
                    else if (!string.IsNullOrEmpty(msg.SqlQuery))
                    {
                        prompt.AppendLine($"Generated SQL: {msg.SqlQuery}");
                    }
                }
            }

            return prompt.ToString();
        }

        private string GetModelName()
        {
            if (_config.Value.Provider.Equals("AzureOpenAI", StringComparison.OrdinalIgnoreCase))
            {
                return _config.Value.AzureOpenAI.DeploymentName;
            }
            return _config.Value.OpenAI.Model;
        }

        private string ExtractSqlFromResponse(string response)
        {
            if (string.IsNullOrWhiteSpace(response))
            {
                throw new InvalidOperationException("Empty response from AI service");
            }

            // Remove common prefixes and suffixes
            var cleanedResponse = response.Trim();

            // Remove markdown code blocks if present
            if (cleanedResponse.StartsWith("```sql", StringComparison.OrdinalIgnoreCase))
            {
                cleanedResponse = cleanedResponse.Substring(6);
            }
            else if (cleanedResponse.StartsWith("```"))
            {
                cleanedResponse = cleanedResponse.Substring(3);
            }

            if (cleanedResponse.EndsWith("```"))
            {
                cleanedResponse = cleanedResponse.Substring(0, cleanedResponse.Length - 3);
            }

            // Remove common prefixes
            var prefixes = new[] { "SELECT", "select", "SQL:", "Query:", "Here's the SQL:" };
            foreach (var prefix in prefixes)
            {
                if (cleanedResponse.StartsWith(prefix, StringComparison.OrdinalIgnoreCase) &&
                    !cleanedResponse.StartsWith("SELECT ", StringComparison.OrdinalIgnoreCase))
                {
                    cleanedResponse = cleanedResponse.Substring(prefix.Length).TrimStart(':').Trim();
                    break;
                }
            }

            cleanedResponse = cleanedResponse.Trim();

            // Validate that it's a SELECT statement
            if (!cleanedResponse.StartsWith("SELECT", StringComparison.OrdinalIgnoreCase))
            {
                throw new InvalidOperationException("Generated query is not a valid SELECT statement");
            }

            // Check for invalid keywords
            var invalidKeywords = _config.Value.BlockedKeywords;
            foreach (var keyword in invalidKeywords)
            {
                if (cleanedResponse.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    throw new InvalidOperationException($"Generated query contains blocked keyword: {keyword}");
                }
            }

            return cleanedResponse;
        }
    }
}
