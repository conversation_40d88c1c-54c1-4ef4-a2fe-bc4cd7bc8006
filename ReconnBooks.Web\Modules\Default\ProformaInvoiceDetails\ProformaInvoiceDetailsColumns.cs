using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.ProformaInvoiceDetails")]
[BasedOnRow(typeof(ProformaInvoiceDetailsRow), CheckNames = true)]
public class ProformaInvoiceDetailsColumns
{
    [DisplayName(""), Width(30), AlignCenter]
    public long RowNumber { get; set; }

    [EditLink, DisplayName("Product/Service Name"), Width(200), LookupInclude]
    public string CommodityName { get; set; }

    [DisplayName("Code"), Width(90)]
    public string CommodityCode { get; set; }

    [EditLink, DisplayName("Type"), Width(60)]
    public string CommodityType { get; set; }

    [DisplayName("HSN/SAC Code"), Width(90)]
    public string HSNSACCode { get; set; }

    [EditLink, DisplayName("Qty."), Width(60), IndianNumberFormatter, AlignCenter]
    public decimal Quantity { get; set; }

    [DisplayName("Unit"), Width(50), ]
    public string UnitName { get; set; }

    [EditLink, DisplayName("Unit Price"), Width(80), IndianNumberFormatter, AlignRight]
    public decimal UnitPrice { get; set; }

    [DisplayName("Unit Amt."), Width(90), IndianNumberFormatter, AlignRight]
    public decimal UnitAmount { get; set; }

    [Hidden, DisplayName("Disc. %"), Width(50), IndianNumberFormatter, AlignRight]
    public decimal DiscountPercent { get; set; }

    [Hidden, DisplayName("Disc.Amt/Unit"), Width(100), IndianNumberFormatter, AlignRight]
    public decimal DiscountAmountPerUnit { get; set; }

    [Hidden, DisplayName("Disc.Amt."), Width(80), IndianNumberFormatter, AlignRight]
    public decimal NetDiscountAmount { get; set; }

    [Hidden, DisplayName("Basic Amt./Unit"), Width(100), IndianNumberFormatter, AlignRight]
    public decimal TaxableAmountPerUnit { get; set; }

    [DisplayName("Net Taxable Amt."), Width(100), IndianNumberFormatter, AlignRight]
    public decimal NetTaxableAmount { get; set; }

    [DisplayName("GST Rate"), Width(60), AlignCenter]
    public string GSTRateRemarks { get; set; }

    [Hidden, DisplayName("IGST %"), Width(60), AlignCenter]
    public decimal IGSTRate { get; set; }

    [Hidden, DisplayName("IGST/Unit"), Width(90), IndianNumberFormatter, AlignRight]
    public decimal IGSTAmountPerUnit { get; set; }

    [Hidden, DisplayName("Net IGST"), Width(90), IndianNumberFormatter, AlignRight]
    public decimal NetIGSTAmount { get; set; }

    [Hidden, DisplayName("CGST %"), Width(60), AlignCenter]
    public decimal CGSTRate { get; set; }

    [Hidden, DisplayName("CGST/Unit"), Width(90), IndianNumberFormatter, AlignRight]
    public decimal CGSTAmountPerUnit { get; set; }

    [Hidden, DisplayName("Net CGST"), Width(90), IndianNumberFormatter, AlignRight]
    public decimal NetCGSTAmount { get; set; }

    [Hidden, DisplayName("SGST %"), Width(60), AlignCenter]
    public decimal SGSTRate { get; set; }
 
    [Hidden, DisplayName("SGST/Unit"), Width(90), IndianNumberFormatter, AlignRight]
    public decimal SGSTAmountPerUnit { get; set; }

    [Hidden, DisplayName("Net SGST"), Width(90), IndianNumberFormatter, AlignRight]
    public decimal NetSGSTAmount { get; set; }

    [EditLink, DisplayName("Net Amount"), Width(100), IndianNumberFormatter, AlignRight]
    public decimal NetAmount { get; set; }
  
    [DisplayName("Net Price/Unit"), Width(90), IndianNumberFormatter, AlignRight]
    public decimal NetPricePerUnit { get; set; }

    [DisplayName("Product Description"), Width(200)]
    public string CommodityDescription { get; set; }

    [DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: false), AlignCenter]
    public int ProformaInvoiceDetailId { get; set; }

    [Hidden]
    public string ProformaInvoiceNo { get; set; }
}