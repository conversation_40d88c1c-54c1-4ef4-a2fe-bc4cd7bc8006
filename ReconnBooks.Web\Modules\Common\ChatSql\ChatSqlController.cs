using Serenity.Data;
using Serenity.Caching;
using Microsoft.AspNetCore.Mvc;
using System.Text;
using System.Threading.Tasks;

namespace ReconnBooks.Web.Modules.Common.ChatSql
{
    [Route("api/chat-sql")]
    [ApiController]
    public class ChatSqlController : ControllerBase
    {
        private readonly ITwoLevelCache _cache;
        private readonly ISqlConnections _connections;
        private readonly string _schemaKey = "DbSchemaInfo";

        public ChatSqlController(ITwoLevelCache cache, ISqlConnections connections)
        {
            _cache = cache;
            _connections = connections;
        }

        [HttpGet("schema")]
        public IActionResult GetSchema()
        {
            var schema = _cache.Get(_schemaKey, TimeSpan.FromHours(6),  FetchDbSchema);
            return Ok(schema);
        }

        private string FetchDbSchema()
        {
            var sb = new StringBuilder();
            using (var connection = _connections.NewByKey("Default"))
            {
                var tables = connection.GetSchema("Tables");
                foreach (DataRow row in tables.Rows)
                {
                    string table = row["TABLE_NAME"].ToString();
                    sb.AppendLine($"Table: {table}");
                    var columns = connection.GetSchema("Columns", new[] { null, null, table });
                    foreach (DataRow col in columns.Rows)
                        sb.AppendLine($"  {col["COLUMN_NAME"]} ({col["DATA_TYPE"]})");
                }
            }
            return sb.ToString();
        }

        [HttpPost("query")]
        public IActionResult Query([FromBody] ChatSqlRequest req)
        {
            // 1. Get schema
            if (!_cache.TryGetValue(_schemaKey, out string schema))
                schema = FetchDbSchema();

            // 2. Call AI service to convert NL to SQL (pseudo, replace with real call)
            string sql = ConvertToSql(req.Message, schema);
            if (string.IsNullOrWhiteSpace(sql))
                return BadRequest("Could not generate SQL query.");

            // 3. Run SQL
            var result = RunSql(sql);
            return Ok(new { sql, result });
        }

        private string ConvertToSql(string message, string schema)
        {
            // TODO: Integrate with OpenAI, Azure OpenAI, or your preferred LLM
            // For now, return null to indicate not implemented
            return null;
        }

        private List<Dictionary<string, object>> RunSql(string sql)
        {
            var results = new List<Dictionary<string, object>>();
            using (var connection = _connections.NewByKey("Default"))
            using (var cmd = connection.CreateCommand())
            {
                cmd.CommandText = sql;
                using (var reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var row = new Dictionary<string, object>();
                        for (int i = 0; i < reader.FieldCount; i++)
                            row[reader.GetName(i)] = reader.GetValue(i);
                        results.Add(row);
                    }
                }
            }
            return results;
        }
    }

    public class ChatSqlRequest
    {
        public string Message { get; set; }
    }
}
