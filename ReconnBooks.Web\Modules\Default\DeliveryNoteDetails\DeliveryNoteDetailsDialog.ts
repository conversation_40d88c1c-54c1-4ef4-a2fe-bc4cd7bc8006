import { DeliveryNoteDetailsForm, DeliveryNoteDetailsRow, CommoditiesRow, GstRatesRow, CommodityTypesRow, CommoditiesService } from '@/ServerTypes/Default';
import { Decorators, toId, EditorUtils, getRemoteData } from '@serenity-is/corelib';
import { PendingChangesConfirmGridEditorDialog } from '../../Common/Helpers/PendingChangesConfirmGridEditorDialog';

@Decorators.registerClass('ReconnBooks.Default.DeliveryNoteDetailsDialog')
export class DeliveryNoteDetailsDialog extends PendingChangesConfirmGridEditorDialog<DeliveryNoteDetailsRow> {

    protected getFormKey() { return DeliveryNoteDetailsForm.formKey; }
    protected getRowDefinition() { return DeliveryNoteDetailsRow; }

    protected form = new DeliveryNoteDetailsForm(this.idPrefix);
    //public PlaceOfSupplyStateId: number;
    public IsSamePlaceOfSupply: boolean;
    protected commodity: CommoditiesRow;
    private previousCommodityValue: string;

    // protected getDialogType() { return DeliveryNoteDetailsGridEditor; }
    //-------- Fetching Product Code from Products table ---------------------
    constructor() {
        super();

        this.form.Quantity.change(e => {
            this.calculateUnitAmount();

            if (this.IsSamePlaceOfSupply) {
                this.calculateCGSTAmounts();
                this.calculateSGSTAmounts();
            }
            else {
                this.calculateIGSTAmounts();
            }

            this.calculateNetAmount();
        })

        this.form.UnitPrice.change(e => {
            this.calculateUnitAmount();

            if (this.IsSamePlaceOfSupply) {
                this.calculateCGSTAmounts();
                this.calculateSGSTAmounts();
            }
            else {
                this.calculateIGSTAmounts();
            }

            this.calculateNetAmount();
        })

        this.form.CommodityCode.changeSelect2(async () => {
            var CommodityCode = toId(this.form.CommodityCode.value);
            if (CommodityCode != null) {
                var Commodity = (await CommoditiesRow.getLookupAsync()).itemById[CommodityCode];
                this.form.CommodityId.value = Commodity.CommodityId.toString();
                this.form.CommodityDescription.value = Commodity.CommodityDescription;
                this.form.CommodityCode.value = Commodity.CommodityId.toString();
                this.form.HSNSACCode.value = Commodity.HSNSACCode;
                this.form.HSNSACDescription.value = Commodity.HSNSACDescription;
                this.form.HSNSACGroup.value = Commodity.HSNSACGroup;
                this.form.Quantity.value = undefined;
                this.form.UnitId.value = Commodity.UnitId.toString();
                this.form.UnitPrice.value = Commodity.SalesPrice;
                if (Commodity.GSTRateId) {
                    this.form.GSTRateId.value = Commodity.GSTRateId.toString();
                    this.applyGST();
                }
            }
        })

        this.form.GSTRateId.changeSelect2(async () => {
            if (!this.form.GSTRateId.value) {
                this.clearGSTFields();
            }
            else {
                this.applyGST();
            }
        });
    }

    private clearGSTFields() {
        this.form.CGSTRate.value = undefined;
        this.form.SGSTRate.value = undefined;
        this.form.IGSTRate.value = undefined;
        this.form.CGSTAmount.value = undefined;
        this.form.SGSTAmount.value = undefined;
        this.form.IGSTAmount.value = undefined;
        this.form.PerUnitCGSTAmount.value = undefined;
        this.form.PerUnitSGSTAmount.value = undefined;
        this.form.PerUnitIGSTAmount.value = undefined;
        this.form.NetAmount.value = undefined;
        this.form.PerUnitPrice.value = undefined;
    }

    protected async afterLoadEntity() {
        await super.afterLoadEntity();

        this.previousCommodityValue = this.form.CommodityId.value;

        this.form.CommodityId.change(e => {
            var commodityId = toId(this.form.CommodityId.value);
            if (commodityId != null) {

                CommoditiesService.Retrieve({
                    EntityId: commodityId
                }, response => {
                    var commodity = response.Entity;
                    this.form.CommodityDescription.value = commodity.CommodityDescription;
                    this.form.CommodityCode.value = commodity.CommodityId.toString();
                    this.form.HSNSACCode.value = commodity.HSNSACCode;
                    this.form.HSNSACDescription.value = commodity.HSNSACDescription;
                    this.form.HSNSACGroup.value = commodity.HSNSACGroup;
                    this.form.Quantity.value = undefined;
                    this.form.UnitId.value = commodity.UnitId.toString();
                    this.form.UnitPrice.value = commodity.SalesPrice;
                    if (commodity.GSTRateId) {
                        this.form.GSTRateId.value = commodity.GSTRateId.toString();
                        this.applyGST();
                    }
                });

                let currentCommodityValue = this.form.CommodityId.value;
                if (this.previousCommodityValue !== currentCommodityValue) {
                    this.form.Quantity.element.focus();
                    this.previousCommodityValue = currentCommodityValue;
                }
            }
            else {
                // clear all fields
                this.clearFields();
            }
        });

        if (this.IsSamePlaceOfSupply){
            this.hideIGST();
            this.showCGST();
            this.showSGST();
        }
        else {
            this.showIGST();
            this.hideCGST();
            this.hideSGST();
        }

        if (this.isNew()) {
            this.form.GSTRateId.value = (await GstRatesRow.getLookupAsync()).items.find(item => item.Current)?.GSTRateId.toString();
            this.form.CommodityTypeId.value = (await CommodityTypesRow.getLookupAsync()).items.find(item => item.SetDefault)?.CommodityTypeId.toString();
        }
        else {
            var commodityType = ((await CommodityTypesRow.getLookupAsync()).itemById[this.form.CommodityTypeId.value]).CommodityType;
            this.form.CommodityCode.value = this.form.CommodityId.value;
            this.form.CommodityTypeId.set_readOnly(true);
            EditorUtils.setReadonly(this.form.CommodityTypeId.element.findFirst('.editor'), true);

            if (this.IsSamePlaceOfSupply) {
                this.calculateCGSTAmounts();
                this.calculateSGSTAmounts();
            }
            else {
                this.calculateIGSTAmounts();
            }
            this.calculateNetAmount();
        }
        this.setDialogsLoadedState();
    }

    async applyGST() {
        var gstRateId = toId(this.form.GSTRateId.value);

        if (gstRateId != null) {
            var gstRate = (await GstRatesRow.getLookupAsync()).itemById[gstRateId];

            if (this.IsSamePlaceOfSupply) {
                this.form.CGSTRate.value = gstRate.CGSTPercent;
                this.form.SGSTRate.value = gstRate.SGSTPercent;
                this.calculateCGSTAmounts();
                this.calculateSGSTAmounts();
            }
            else {
                this.form.IGSTRate.value = gstRate.IGSTPercent;
                this.calculateIGSTAmounts();
            }
            this.calculateNetAmount();
        }
    }

    calculateNetAmount() {
        var unitAmount = this.form.UnitAmount.value;
        var cgstAmount = this.form.CGSTAmount.value;
        var sgstAmount = this.form.SGSTAmount.value;
        var igstAmount = this.form.IGSTAmount.value;

        this.form.NetAmount.value = unitAmount + (this.IsSamePlaceOfSupply ? cgstAmount + sgstAmount : igstAmount);
        this.form.PerUnitPrice.value = this.form.NetAmount.value / this.form.Quantity.value;
    }

    calculateIGSTAmounts() {
        var igstRate = this.form.IGSTRate.value;
        var unitAmount = this.form.UnitAmount.value;
        var quantity = this.form.Quantity.value;

        var netUnitAmount = igstRate * unitAmount / 100;
        this.form.IGSTAmount.value = netUnitAmount;
        this.form.PerUnitIGSTAmount.value = netUnitAmount / quantity;
    }

    calculateCGSTAmounts() {
        var cgstRate = this.form.CGSTRate.value;
        var unitAmount = this.form.UnitAmount.value;
        var quantity = this.form.Quantity.value;

        var netUnitAmount = cgstRate * unitAmount / 100;
        this.form.CGSTAmount.value = netUnitAmount;
        this.form.PerUnitCGSTAmount.value = netUnitAmount / quantity;
    }

    calculateSGSTAmounts() {
        var sgstRate = this.form.SGSTRate.value;
        var unitAmount = this.form.UnitAmount.value;
        var quantity = this.form.Quantity.value;

        var netUnitAmount = sgstRate * unitAmount / 100;
        this.form.SGSTAmount.value = netUnitAmount;
        this.form.PerUnitSGSTAmount.value = netUnitAmount / quantity;
    }

    calculateUnitAmount() {
        var quantity = this.form.Quantity.value;
        var unitPrice = this.form.UnitPrice.value;

        this.form.UnitAmount.value = quantity * unitPrice;
    }

    showCGST() {
        this.element.findFirst(".CGSTRate").show();
        this.element.findFirst(".PerUnitCGSTAmount").show();
        this.element.findFirst(".CGSTAmount").show();
    }

    hideCGST() {
        this.element.findFirst(".CGSTRate").hide();
        this.element.findFirst(".PerUnitCGSTAmount").hide();
        this.element.findFirst(".CGSTAmount").hide();
    }

    showSGST() {
        this.element.findFirst(".SGSTRate").show();
        this.element.findFirst(".PerUnitSGSTAmount").show();
        this.element.findFirst(".SGSTAmount").show();
    }

    hideSGST() {
        this.element.findFirst(".SGSTRate").hide();
        this.element.findFirst(".PerUnitSGSTAmount").hide();
        this.element.findFirst(".SGSTAmount").hide();
    }

    showIGST() {
        this.element.findFirst(".IGSTRate").show();
        this.element.findFirst(".PerUnitIGSTAmount").show();
        this.element.findFirst(".IGSTAmount").show();
    }

    hideIGST() {
        this.element.findFirst(".IGSTRate").hide();
        this.element.findFirst(".PerUnitIGSTAmount").hide();
        this.element.findFirst(".IGSTAmount").hide();
    }

    protected updateTitle() {
        this.dialogTitle = "Edit Delivery Note Details";
    }
    clearFields() {
        this.form.CommodityDescription.value = undefined;
        this.form.CommodityCode.value = undefined;
        this.form.HSNSACCode.value = undefined;
        this.form.HSNSACDescription.value = undefined;
        this.form.HSNSACGroup.value = undefined;
        this.form.Sku.value = undefined;
        this.form.Quantity.value = undefined;
        this.form.UnitId.value = undefined;
        this.form.UnitAmount.value = undefined;
        this.form.UnitPrice.value = undefined;
        this.form.GSTRateId.value = undefined;
        this.form.CGSTRate.value = undefined;
        this.form.SGSTRate.value = undefined;
        this.form.IGSTRate.value = undefined;
        this.form.CGSTAmount.value = undefined;
        this.form.PerUnitCGSTAmount.value = undefined;
        this.form.SGSTAmount.value = undefined;
        this.form.PerUnitSGSTAmount.value = undefined;
        this.form.IGSTAmount.value = undefined;
        this.form.PerUnitIGSTAmount.value = undefined;
        this.form.NetAmount.value = undefined;
        this.form.PerUnitPrice.value = undefined;
    }
}

