@using ReconnBooks.Modules.Common.Helpers;
@model ReconnBooks.Modules.Default.DeliveryNotes.Reporting.DeliveryNotesReportData
@{
    Layout = "";
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delivery Notes</title>
    <!-- Include Bootstrap CSS from CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0px 0px 10px 15px; /* top right bottom left */
            padding: 0px;
            font-family: Calibri, sans-serif;
        }

        .header-table,
        .DeliveryNote-table,
        .Customer-table,
        .Product-table {
            width: 100%; /* Ensures the table takes full width */
            border-collapse: collapse;
            margin-bottom: 0px;
        }

        .Customer-table,
        .header-table td,
        .DeliveryNote-table td {
            padding: 3px!important;
            vertical-align: middle;
        }

        .qr-code {
            width: 80px;
            height: auto; 
        }

        .logo {
            max-width: 100%; 
            height: auto; 
        }

        .address-container {
            text-align: left;
            word-wrap: break-word; /* Ensure text wraps within column */
        }
        
        .address-container strong {
            font-size: 25px;
            padding: 1px;
        }

        .DeliveryNote-table .DeliveryNote-table-cell {
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            padding: 3px;
            background-color: #f0f0f0;
            border: 0.25px solid black;
        }

        .header-table .DeliveryNote-table-cell {
            border: 0.25px solid black;
        }

        .header-table {
            table-layout: fixed; /* Prevent columns from resizing */
        }

        .Product-table {
            width: 100%;
            table-layout: fixed; /* Ensures fixed column widths */
            border-collapse: collapse;
            margin-top: 0px;
            border: 0.25px solid black;
            padding: 3px;
        }
        
        .Product-table td {
            padding: 3px;
            text-align: left;
            border-left: 0.25px solid black; /* Vertical lines only */
            vertical-align: top; /* Align header content at the top */
            line-height: 1.1 !important;
        }

        .Product-table th {
            padding: 2px;
            text-align: center !important;
            border-left: 0.25px solid black;
            font-size: 16px;
            height: 40px !important;
            line-height: 1.1 !important;
        }
        
        .Product-table col {
            width: 10px; /* Adjust percentages as needed */
        }
        
        .Product-table .desc-col {
            width: 58px; /* Wider for the description column */
            text-align: left;
        }
        
        .Product-table th:first-child,
        .Product-table td:first-child {
            border-left: none; /* Remove the first vertical border */
        }
        
        .Product-table td {
            border-top: none; /* Remove horizontal lines */
            border-bottom: none; /* Remove horizontal lines */
        }

        .Product-table th {
            background-color: #f0f0f0;
        }
        
        .totals-row td {
            background-color: #f0f0f0;
            vertical-align: top; /* Align header content at the top */
        }
        
        .Product-table td, .Product-table th {
            word-wrap: break-word; /* Break long words */
            word-break: break-word; /* Break long words */
            white-space: normal; /* Allow wrapping */
        }

        .summary-table {
            width: 100%;
            margin-top: 0px;
            border: 0.25px solid black;
            border-collapse: collapse; /* Ensures a clean border appearance */
            min-height: 150px;
        }
        
        .summary-table td {
            padding: 3px;
            vertical-align: top;
            font-size: 15px;
        }

        .summary-left div {
            display: flex;
            align-items: baseline; /* Align text vertically */
            margin-bottom: 8px !important; /* Space between rows */
        }

        .summary-left .label {
            width: 20px; /* Adjust label width */
            text-align: left;
        }

        .summary-left .colon {
            width: 1px; /* Adjust width for colon */
            text-align: center;
        }

        .summary-left .value {
            margin-left: 10px; /* Space after colon */
            flex-grow: 1; /* Let value take remaining space */
        }

        .summary-right {
            width: 40px; /* Retains the width of the right section */
            text-align: right;
        }
        
        .summary-right .entry {
            display: flex;
            justify-content: flex-end; /* Aligns items to the right */
            margin-bottom: 8px; /* Adjusts space between entries */
        }
        
        .summary-right .label {
            display: inline-block;
            width: 150px;
            text-align: right; /* Aligns label to the right */
        }
        
        .summary-right .colon {
            display: inline-block;
            width: 20px; /* Adjusts the space between label and value */
            text-align: center; /* Keeps colon centered */
        }
        
        .summary-right .value {
            display: inline-block;
            width: 150px;
            text-align: right; /* Ensures value is aligned to the right */
            padding-left: 15px; /* Space between colon and value */
        }

        .summary-left .label,
        .summary-left .colon,
        .summary-left .value {
            font-size: 17px !important;
        }

        .summary-right .label,
        .summary-right .colon,
        .summary-right .value {
            font-size: 18px !important;
        }

        /* delivery Info and Date Styling */
        .delivery-info,
        .deliveryNo-info,
        .date-info {
            text-align: left;
            border: 0.25px solid black;
            padding: 1px 5px;
            line-height: 1;
            font-size: 16px;
            height: 30px; /* Set a fixed height */
            overflow: hidden; /* Prevents text from increasing row height */
            vertical-align: middle; /* Ensures text stays centered */
        }

        .delivery-info {
            width: 15%;
            padding: 0px;
        }

        .deliveryNo-info {
            width: 18%;
            padding: 0px;
        }

        .date-info {
            width: 8%;
            padding: 0px;
        }
        /* Customer Table Styling */
        .Customer-table {
            border-collapse: collapse;
            width: 100%;
        }

        /* Container for Customer Info and Shipping Address */
        .customer-info-container {
            position: relative;
            padding-bottom: 0px;
            padding-top: 0px;
            margin-top: -20px;
        }

        /* Customer Info */
        .customer-info {
            vertical-align: top;
            padding-left: 3px;
            width: 100%;
        }
        /* Entry line styling for colon alignment */
        .entry-line {
            display: flex;
            align-items: flex-start !important;
            padding-left: 3px; 
        }
        
        .entry-line .label {
            display: inline-block;
            width: 100px;
        }
        
        .entry-line .colon {
            display: inline-block;
            width: 5px;
            text-align: center;
        }
        
        .entry-line .value {
            text-align: left;
            padding-left: 2px;
            flex-grow: 1;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .Customer-table td strong {
            font-weight: bold;
        }

        .footer-note {
            margin-top: 0px;
            text-align: right;
            padding: 3px;
        }

        .header-table {
            width: 100vw;
            margin-left: -20px;
            margin-right: -20px;
            border-collapse: collapse;
        }

        .header-container, .quotation-container {
            margin-bottom: 0px !important;
            padding-bottom: 0px !important;
        }
        
        .header-table td {
            padding: 2px !important;
            vertical-align: middle;
        }

        .dynamic-height-row {
            height: 50px; /* Default height that will be multiplied by the number of rows */
        }
        
        .dynamic-height-row td {
            border-left: 0.25px solid black; /* Match the border style of other cells */
            border-right: none;
            border-top: none;
            border-bottom: none;
        }
        
        .dynamic-height-row td:first-child {
            border-left: none;
        }
      
        .Product-table {
            table-layout: fixed; /* Ensure fixed column widths */
            width: 100%;
        }
        
        .Product-table tbody {
            display: table-row-group;
        }
      
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        .container-fluid {
            min-height: 100%;
            height: auto;
            width: 100%;
            box-sizing: border-box;
        }
    </style>
</head>
<body>

    <div class="container-fluid">
        <!-- Header Table -->
        <table class="header-table  ">
            <tr>
                <!-- Logo Column -->
                <td class="col-sm-2 text-center">
                    <img src="/upload/@Model.Client.Logo" alt="" class="logo" style="@(string.IsNullOrWhiteSpace(Model.Client.Logo) ? "display: none;" : "")" />
                </td>

                <!-- Address Column -->
                <td class="col-sm-9 address-container" style="line-height: 1.3;">
                    <div style="font-size: 27px; text-align: center;"><strong>@Model.Client.ClientName</strong></div>
                    <div style="font-size: 17px; text-align: center;">
                        <div style="font-weight: bold;">@Model.Client.Address, @Model.Client.CityName - @Model.Client.PINCode<br /></div>

                        <div style="font-weight: bold;">
                            @(string.Join(" | ", new[] {
                            Model.Client.GSTIN != null ? $"GSTIN: {Model.Client.GSTIN}" : null,
                            Model.Client.PlaceOfSupplyStateCode != null ? $"{Model.Client.PlaceOfSupplyStateCode} - {Model.Client.PlaceOfSupplyStateCodeNo}" : null,
                            Model.Client.PAN != null ? $"PAN: {Model.Client.PAN}" : null,
                            Model.Client.UdyamNo != null ? $"UDYAM No.: {Model.Client.UdyamNo}" : null
                            }.Where(x => x != null)))<br />
                        </div>
                        
                        @(string.Join(" | ", new[] {
                        Model.Client.HomePage,
                        Model.Client.EMail != null ? $"Email: {Model.Client.EMail}" : null,
                        Model.Client.PhoneNo != null ? $"Phone: {Model.Client.PhoneNo}" : (Model.Client.MobileNo != null ? $"Phone: {Model.Client.MobileNo}" : null)
                        }.Where(x => x != null)))<br />

                        @(string.Join(" | ", new[] {
                        Model.Client.CINNo != null ? $"CIN: {Model.Client.CINNo}" : null,
                        Model.Client.IECNo != null ? $"IECNo: {Model.Client.IECNo}" : null
                        }.Where(x => x != null)))
                    </div>

                    <div style="font-size: 17px!important; text-align: center; font-weight: bold;">@(Model.Client.TagLine != null ? Model.Client.TagLine : string.Empty)</div>
                </td>

                <!-- QR Code Column -->
                <td class="col-sm-2 text-center">
                    @{
                        if (!string.IsNullOrEmpty(Model.Client.Logo))

                        {

                            <img class="qr-code">
                        }
                    }
                </td>
            </tr>
        </table>

        <!-- DeliveryNote Table -->
        <table class="DeliveryNote-table" style="border-bottom:none!important;">
            <tr>
                <td class="DeliveryNote-table-cell">DELIVERY NOTE</td>
            </tr>
        </table>

        <!-- Customer Table -->
        <table class="Customer-table" style="border: 0.25px solid black; border-collapse: collapse; width: 100%;">
            <tr>
                <!-- Customer Info and Shipping Address Container -->
                <td class="customer-info-container" rowspan="7" style="vertical-align: top; width: 48%; border-right: 0.25px solid black;">
                    <div class="customer-info" style="font-size: 18px; padding-left: 3px; margin: 0; width: 100%; box-sizing: border-box;">
                        <span>Customer Name:</span><br>
                        <span><strong>@Model.Customer.CompanyName</strong></span><br>
                        <span>
                            @Model.Customer.BillingAddress<br />
                            @Model.Customer.BillingCityCityName - @Model.Customer.BillingPinCode
                        </span><br>
                        @if (!string.IsNullOrWhiteSpace(Model.Customer.PAN) || !string.IsNullOrWhiteSpace(Model.Customer.UdyamNo))

                        {
                            <span style="display: block; margin-bottom: 10px;">
                                <strong>GST No.:@Model.Customer.GSTIN</strong>,
                                @Model.Customer.PlaceOfSupplyStateName
                            </span>
                        }

                        @if (Model.DeliveryNotes.ShippingAddress != null || Model.DeliveryNotes.ShipToCustomerName != null || Model.DeliveryNotes.ShippingCityName != null || Model.DeliveryNotes.ShippingPinCode != null || Model.DeliveryNotes.ShippingGSTIN != null)

                        {
                            <span style="display: block; margin-top: 10px; text-decoration-line: underline;">Shipping Address:</span>
                            <span style="display: block;">
                                @Html.Raw(Model.DeliveryNotes.ShipToCustomerName != null ? $"<strong>{Model.DeliveryNotes.ShipToCustomerName}</strong><br />" : string.Empty)
                                @(Model.DeliveryNotes.ShippingAddress != null ? $"{Model.DeliveryNotes.ShippingAddress}" : string.Empty)<br />
                                @(Model.DeliveryNotes.ShippingCityName != null ? $"{Model.DeliveryNotes.ShippingCityName}" : string.Empty)
                                @(Model.DeliveryNotes.ShippingPinCode != null ? $" - {Model.DeliveryNotes.ShippingPinCode}" : string.Empty)
                                @if (Model.DeliveryNotes.ShippingCityName != null || Model.DeliveryNotes.ShippingPinCode != null)
                                {
                                    <br />
                                }
                                @(Model.DeliveryNotes.ShippingGSTIN != null ? $"GST No.: {Model.DeliveryNotes.ShippingGSTIN}" : string.Empty)
                                @(Model.DeliveryNotes.ShippingPlaceOfSupplyStateName != null ? $", {Model.DeliveryNotes.ShippingPlaceOfSupplyStateName}" : string.Empty)
                            </span>
                        }
                    </div>

                    <!-- delivery Info -->
                <td class="delivery-info" style="font-size: 17px; padding: 3px; padding-left: 3px; font-weight: bold;">
                    DeliveryNote No.
                </td>
                <td class="deliveryNo-info" style="font-size: 17px; padding: 3px; padding-left: 3px; border-right: none; font-weight: bold;">
                        @Model.DeliveryNotes.DeliveryNoteNo
                </td>
                <td class="date-info" style="font-size: 17px; padding: 3px; padding-left: 3px; border-left: none; font-weight: bold;">
                        @Model.DeliveryNotes.DeliveryNoteDate.IndianFormatDate()
                </td>
            </tr>
            <tr>
                <td class="delivery-info" style="font-size: 17px; padding: 3px; padding-left: 3px;">
                    E-way Bill No.
                </td>
                <td class="deliveryNo-info" style="font-size: 17px; padding: 3px; padding-left: 3px; border-right: none;">
                    @Model.DeliveryNotes.EWayBillNo
                </td>
                <td class="date-info" style="font-size: 17px; padding: 3px; padding-left: 3px; border-left: none;">
                    @Model.DeliveryNotes.EWayBillNoDate.IndianFormatDate()
                </td>
            </tr>
            <tr>
                <td class="delivery-info" style="font-size: 17px; padding: 3px; padding-left: 3px;">
                    Order Ref No.
                </td>
                <td class="deliveryNo-info" style="font-size: 17px; padding: 3px; padding-left: 3px; border-right: none;">
                    @Model.DeliveryNotes.SalesOrderRefNo
                </td>
                <td class="date-info" style="font-size: 17px; padding: 3px; padding-left: 3px; border-left: none;">
                    @Model.DeliveryNotes.SalesOrderRefDate.IndianFormatDate()
                </td>
            </tr>
            <tr>
                <td class="delivery-info" style="font-size: 17px; padding: 3px; padding-left: 3px;">
                    Purchase Order No.
                </td>
                <td class="deliveryNo-info" style="font-size: 17px; padding: 3px; padding-left: 3px; border-right: none;">
                </td>
                <td class="date-info" style="font-size: 17px; padding: 3px; padding-left: 3px; border-left: none;">
                </td>
            </tr>
            <tr>
                <!-- New row for Shipping Address, Docket No., Vehicle No., Payment Terms, and Payment Due Date -->
            <tr>
                <td class="shipping-info" colspan="4" style="vertical-align: top; padding-top: 5px;">
                        @if (Model.DeliveryNotes.ShippedVia != null)

                        {
                        <div class="entry-line" style="font-size: 17px; display: flex; align-items: center; line-height: 1.5 !important;">
                            <span class="label" style="line-height: 1.5 !important;">Shipped Via.</span>
                            <span class="colon" style="line-height: 1.5 !important; width: 5px;">:</span>
                            <span class="value" style="line-height: 1.5 !important;">@Model.DeliveryNotes.ShippedVia</span>
                        </div>
                        }
                        @if (Model.DeliveryNotes.ShippingDocketNo != null)

                        {
                        <div class="entry-line" style="font-size: 17px; display: flex; align-items: center; line-height: 1.5 !important;">
                            <span class="label" style="line-height: 1.5 !important;">Docket No.</span>
                            <span class="colon" style="line-height: 1.5 !important; width: 5px;">:</span>
                            <span class="value" style="line-height: 1.5 !important;">@Model.DeliveryNotes.ShippingDocketNo</span>
                        </div>
                        }
                        @if (Model.DeliveryNotes.VehicleNo != null)

                        {
                        <div class="entry-line" style="font-size: 17px; display: flex; align-items: center; line-height: 1.5 !important;">
                            <span class="label" style="line-height: 1.5 !important;">Vehicle No.</span>
                            <span class="colon" style="line-height: 1.5 !important; width: 5px;">:</span>
                            <span class="value" style="line-height: 1.5 !important;">@Model.DeliveryNotes.VehicleNo</span>
                        </div>
                        }
                </td>
            </tr>
        </table>

        <table class="Product-table">
            <colgroup>
                <col style="width: 5px;"> <!-- Sl. No. -->
                <col class="desc-col"> <!-- Description -->
                <col style="width: 14px;"> <!-- HSN/SAC Code -->
                <col style="width: 12px;"> <!-- Qty -->
                <col style="width: 10px;"> <!-- Unit -->
                <col style="width: 18px;"> <!-- Net Amt. -->
            </colgroup>
            <thead>
                <tr>
                    <th>Sl. No.</th>
                    <th>Product/Service Description</th>
                    <th>HSN/SAC Code</th>
                    <th>Qty.</th>
                    <th>Unit</th>
                    <th>Net Amt.</th>
                </tr>
            </thead>
            <tbody>
                @{
                    var counter = 1;

                    foreach (var deliveryNotesDetail in Model.DeliveryNotes.DeliveryNoteDetailsList)

                    {
                        <tr>
                            <td style="font-size: 17px; text-align: center">
                                @(counter++.ToString("#,##0"))
                            </td>
                            <td style="width: 90%; text-align: left; font-size: 18px; line-height: 1.1;">
                                <strong>@deliveryNotesDetail.CommodityName</strong><br />
                                @if (!string.IsNullOrEmpty(deliveryNotesDetail.CommodityDescription))

                                {
                                    <span style="font-size: 16px;">@deliveryNotesDetail.CommodityDescription</span>

                                    <br />
                                }
                                @if (!string.IsNullOrEmpty(deliveryNotesDetail.CommodityCode))

                                {
                                    <span style="font-size: 16px;">Part No.: @deliveryNotesDetail.CommodityCode</span>

                                    <br />
                                }
                                @if (!string.IsNullOrEmpty(deliveryNotesDetail.HSNSACCode))

                                {
                                    <span style="font-size: 16px;">HSN/SAC Code: @deliveryNotesDetail.HSNSACCode</span>
                                }
                            </td>
                            <td style="text-align: left; font-size: 17px">@deliveryNotesDetail.HSNSACCode</td>
                            <td style="text-align: center; font-size: 17px">@deliveryNotesDetail.Quantity?.ToString("#,##0.##")</td>
                            <td style="text-align: center; font-size: 17px">@deliveryNotesDetail.UnitName</td>
                            <td style="text-align: right; font-size: 17px"><strong>@(deliveryNotesDetail.NetAmount?.IndianFormat() ?? "")</strong></td>
                        </tr>
                    }

                    var itemCount = Model.DeliveryNotes.DeliveryNoteDetailsList?.Count ?? 0;

                    var minRows = itemCount < 10 ? (10 - itemCount) : 0;

                    for (int i = 0; i < minRows; i++)

                    {
                        <tr class="dynamic-height-row">
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                    }
                }
            </tbody>
            <tfoot>
                @{
                    bool isIntraState = Model.Customer.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId;

                    int columnCount = 6;

                    string rowHeight = "1px";
                }
                <tr style="line-height: 1px; height: @rowHeight;">
                    @for (int i = 0; i < columnCount; i++)

                    {
                        <td style="border-bottom: 1px solid black;"></td>
                    }
                </tr>
            </tfoot>
        </table>

        <table class="Product-table" style="margin-top: 0px!important; border-top: none!important;height: 35px;">
            <colgroup>
                <col style="width: 5px;"> <!-- Sl. No. -->
                <col class="desc-col"> <!-- Description -->
                <col style="width: 14px;"> <!-- HSN/SAC Code -->
                <col style="width: 12px;"> <!-- Qty -->
                <col style="width: 10px;"> <!-- Unit -->
                <col style="width: 18px;"> <!-- Net Amt. -->
            </colgroup>
            <tbody>
                <tr class="totals-row">
                    <td style="font-size: 17px"></td>
                    <td style="font-size: 17px;text-align: right; vertical-align: middle">Total</td>
                    <td style="font-size: 17px"></td>
                    <td style="font-size: 17px"></td>
                    <td style="font-size: 17px"></td>
                    <td style="text-align: right; font-size: 17px; vertical-align: middle"><strong>@Model.DeliveryNotes.DeliveryNoteDetailsList.Sum(d => d.NetAmount ?? 0).IndianFormat()</strong></td>
                </tr>
            </tbody>
            </table>

        <table class="summary-table">
            <tr>
                <!-- Terms & Conditions -->
                <td class="summary-left">
                    <div><strong>Remarks:</strong>@Model.DeliveryNotes.Remarks</div>
                </td>

                <!-- Totals -->
                <td class="summary-right">
                    <div class="entry">
                        <span class="label"><strong>Total Value</strong></span>
                        <span class="colon">:</span>
                        <span class="value"><strong>₹ @Model.DeliveryNotes.DeliveryNoteDetailsList.Sum(d => d.NetAmount ?? 0).IndianFormat()</strong></span>
                    </div>
                </td>
            </tr>
        </table>

        <!-- Remarks Section -->
        <table style="width: 100%;  border: 0.25px solid black; border-top: none; border-collapse: collapse; margin-top: 0px;">
            <tr>

                <!-- Remarks Section -->
                <td style="width: 60%; text-align: left; vertical-align: top; padding: 3px; border: none; font-size: 17px;">
                    <strong></strong><br>
                </td>

                <!-- Authorised Signatory Section -->
                <td style="width: 40%; text-align: center; vertical-align: middle; padding: 10px; border: none;">
                    <div style="display: inline-block; width: 100%; text-align: center; font-size: 17px;">
                        <strong> For @Model.DeliveryNotes.ClientName</strong>

                        <!-- Digital Signature (Fixed Space for Image) -->
                        <div id="signature-container" style="margin-top: 10px; margin-bottom: 10px; height: 80px;">
                            <img src='/upload/@Model.Client.ClientDSC'
                                 style='max-width: 300px; max-height: 80px; background-color: transparent; height: 80px; width: auto;'
                                 onerror="this.parentElement.style.height='50px'; this.style.display='none';" />
                        </div>

                        <span style="font-size: 16px;">Authorised Signatory</span>
                    </div>
                </td>
            </tr>
        </table>
        <div class="Powered-by" style="font-size: 12px; margin-top: 2px; word-wrap: break-word;">
            Powered by<strong> www.reconnbooks.com</strong>, hosted by <strong>Reconn Info Solutions India Pvt. Ltd.</strong>
        </div>
    </div>
</body>
</html> 