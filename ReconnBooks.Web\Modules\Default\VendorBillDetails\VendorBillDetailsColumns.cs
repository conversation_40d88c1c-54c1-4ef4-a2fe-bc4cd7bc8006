using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.VendorBillDetails")]
[BasedOnRow(typeof(VendorBillDetailsRow), CheckNames = true)]
public class VendorBillDetailsColumns
{
    [DisplayName(""), Width(30), AlignCenter]
    public long RowNumber { get; set; }

    [EditLink, DisplayName("Product/Service Name"), Width(200), LookupInclude]
    public string CommodityName { get; set; }

    [DisplayName("Code"), Width(90)]
    public string CommodityCode { get; set; }

    [EditLink, DisplayName("Type"), Width(60)]
    public string CommodityType { get; set; }
   
    [DisplayName("HSN/SAC Code"), Width(90)]
    public string HSNSACCode { get; set; }

    [EditLink, DisplayName("Qty."), <PERSON>idth(60), AlignCenter]
    public decimal BillQuantity { get; set; }

    [DisplayN<PERSON>("Unit"), Width(50)]
    public string UnitName { get; set; }

    [EditLink, DisplayName("Unit Price"), Width(80), IndianNumberFormatter, AlignRight]
    public decimal UnitPrice { get; set; }

    [DisplayName("Unit Amt."), Width(90), IndianNumberFormatter, AlignRight]
    public decimal UnitAmount { get; set; }

    [Hidden, DisplayName("Disc. %"), Width(50), IndianNumberFormatter, AlignRight]
    public decimal DiscountPercent { get; set; }

    [Hidden, DisplayName("Net Disc.Amt."), Width(80), IndianNumberFormatter, AlignRight]
    public decimal DiscountAmount { get; set; }
  
    [DisplayName("Net Taxable Amt."), Width(100), IndianNumberFormatter, AlignRight]
    public decimal NetTaxableAmount { get; set; }

    [DisplayName("GST Rate"), Width(60), AlignCenter]
    public string GSTRateRemarks { get; set; }

    [DisplayName("IGST %"), Width(50), AlignCenter]
    public decimal IGSTRate { get; set; }

    [DisplayName("Net IGST"), Width(90), IndianNumberFormatter, AlignRight]
    public decimal NetIGSTAmount { get; set; }

    [DisplayName("CGST %"), Width(52), AlignCenter]
    public decimal CGSTRate { get; set; }

    [DisplayName("Net CGST"), Width(90), IndianNumberFormatter, AlignRight]
    public decimal NetCGSTAmount { get; set; }

    [DisplayName("SGST %"), Width(50), AlignCenter]
    public decimal SGSTRate { get; set; }

    [DisplayName("SGST/Unit"), Width(80), IndianNumberFormatter, AlignRight]
    public decimal NetSGSTAmount { get; set; }

    [EditLink, DisplayName("Net Amount"), Width(100), IndianNumberFormatter, AlignRight]
    public decimal NetAmount { get; set; }

    [Hidden]
    public int VendorBillId { get; set; }

    [Hidden]
    public string PurchaseOrderDetailCommodityDescription { get; set; }

    [DisplayName("Product Description"), Width(200)]
    public string CommodityDescription { get; set; }

    [DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: false), AlignCenter]
    public int VendorBillDetailId { get; set; }

}