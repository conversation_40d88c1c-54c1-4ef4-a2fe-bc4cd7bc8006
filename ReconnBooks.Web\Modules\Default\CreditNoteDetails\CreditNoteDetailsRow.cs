using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("CreditNoteDetails")]
[DisplayName("Credit Note Details"), InstanceName("Credit Note Details"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[LookupScript]
public sealed partial class CreditNoteDetailsRow : Row<CreditNoteDetailsRow.RowFields>, IIdRow, INameRow, IRowNumberedRow
{
    const string jCreditNote = nameof(jCreditNote);
    const string jInvoiceDetail = nameof(jInvoiceDetail);
    const string jSalesReturnDetail = nameof(jSalesReturnDetail);
    const string jCommodity = nameof(jCommodity);
    const string jCommodityType = nameof(jCommodityType);
    const string jInvoiceUnit = nameof(jInvoiceUnit);
    const string jReturnedUnit = nameof(jReturnedUnit);
    const string jGSTRate = nameof(jGSTRate);
    const string jRejectionReason = nameof(jRejectionReason);
    const string jReplacementMethod = nameof(jReplacementMethod);

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

    [DisplayName("Credit Note Detail Id"), Identity, IdProperty, NameProperty]
    public int? CreditNoteDetailId { get => fields.CreditNoteDetailId[this]; set => fields.CreditNoteDetailId[this] = value; }

    [DisplayName("Credit Note"), NotNull, ForeignKey(typeof(CreditNotesRow)), LeftJoin(jCreditNote), TextualField(nameof(CreditNoteNo))]
    [ServiceLookupEditor(typeof(CreditNotesRow), Service = "Default/CreditNotes/List")]
    public int? CreditNoteId { get => fields.CreditNoteId[this]; set => fields.CreditNoteId[this] = value; }

    [DisplayName("Invoice Detail"), ForeignKey(typeof(InvoiceDetailsRow)), LeftJoin(jInvoiceDetail)]
    [TextualField(nameof(InvoiceDetailCommodityDescription)), LookupEditor(typeof(InvoiceDetailsRow), Async = true)]
    public int? InvoiceDetailId { get => fields.InvoiceDetailId[this]; set => fields.InvoiceDetailId[this] = value; }

    [DisplayName("Sales Return Detail"), ForeignKey(typeof(SalesReturnDetailsRow)), LeftJoin(jSalesReturnDetail)]
    [TextualField(nameof(SalesReturnDetailCommodityDescription))]
    [ServiceLookupEditor(typeof(SalesReturnDetailsRow), Service = "Default/SalesReturnDetails/List")]
    public int? SalesReturnDetailId { get => fields.SalesReturnDetailId[this]; set => fields.SalesReturnDetailId[this] = value; }

    //--Commodities--

    [DisplayName("Commodity Type"), NotNull, ForeignKey(typeof(CommodityTypesRow)), LeftJoin(jCommodityType)]
    [TextualField(nameof(CommodityType))]
    [ServiceLookupEditor(typeof(CommodityTypesRow), Service = "Default/CommodityTypes/List")]
    public int? CommodityTypeId { get => fields.CommodityTypeId[this]; set => fields.CommodityTypeId[this] = value; }

    [DisplayName("Commodity"), NotNull, ForeignKey(typeof(CommoditiesRow)), LeftJoin(jCommodity), TextualField(nameof(CommodityName))]
    [ServiceLookupEditor(typeof(CommoditiesRow), InplaceAdd = true, Service = "Default/Commodities/List", CascadeFrom = "CommodityTypeId", CascadeField = "CommodityTypeId")]
    [LookupInclude]
    public long? CommodityId { get => fields.CommodityId[this]; set => fields.CommodityId[this] = value; }

    //--Fetching Product Code from Products Master--

    [DisplayName("Product Code")]
    [Origin(jCommodity, nameof(CommoditiesRow.CommodityCode)), LookupInclude]
    [CommodityCodeEditor(CascadeFrom = "CommodityTypeId", CascadeField = "CommodityTypeId")]
    public string CommodityCode { get => fields.CommodityCode[this]; set => fields.CommodityCode[this] = value; }

    //--Commodity details--

    [DisplayName("Commodity Description"), QuickSearch, LookupInclude]
    public string CommodityDescription { get => fields.CommodityDescription[this]; set => fields.CommodityDescription[this] = value; }

    [DisplayName("Commodity Type"), Origin(jCommodityType, nameof(CommodityTypesRow.CommodityType)), LookupInclude]
    public string CommodityType { get => fields.CommodityType[this]; set => fields.CommodityType[this] = value; }

    [DisplayName("Commodity Name"), Origin(jCommodity, nameof(CommoditiesRow.CommodityName)), LookupInclude]
    public string CommodityName { get => fields.CommodityName[this]; set => fields.CommodityName[this] = value; }

    [DisplayName("Invoice Quantity"), Size(18), Scale(2), NotNull]
    public decimal? InvoiceQuantity { get => fields.InvoiceQuantity[this]; set => fields.InvoiceQuantity[this] = value; }

    [DisplayName("Invoice Unit"), NotNull, ForeignKey(typeof(UnitsRow)), LeftJoin(jInvoiceUnit), TextualField(nameof(InvoiceUnitUnitName))]
    [LookupEditor(typeof(UnitsRow), Async = true)]
    public int? InvoiceUnitId { get => fields.InvoiceUnitId[this]; set => fields.InvoiceUnitId[this] = value; }

    [DisplayName("Returned Quantity"), Size(18), Scale(2), NotNull]
    public decimal? ReturnedQuantity { get => fields.ReturnedQuantity[this]; set => fields.ReturnedQuantity[this] = value; }

    [DisplayName("Returned Unit"), NotNull, ForeignKey(typeof(UnitsRow)), LeftJoin(jReturnedUnit)]
    [TextualField(nameof(ReturnedUnitUnitName)), LookupEditor(typeof(UnitsRow), Async = true)]
    public int? ReturnedUnitId { get => fields.ReturnedUnitId[this]; set => fields.ReturnedUnitId[this] = value; }

    [DisplayName("Serial Nos")]
    public string SerialNos { get => fields.SerialNos[this]; set => fields.SerialNos[this] = value; }

    [DisplayName("Unit Price"), Size(18), Scale(2), NotNull]
    public decimal? UnitPrice { get => fields.UnitPrice[this]; set => fields.UnitPrice[this] = value; }

    [DisplayName("Unit Amount"), Size(18), Scale(2), NotMapped]
    public decimal? UnitAmount { get => fields.UnitAmount[this]; set => fields.UnitAmount[this] = value; }

    [DisplayName("Discount Percent"), Size(18), Scale(2)]
    public decimal? DiscountPercent { get => fields.DiscountPercent[this]; set => fields.DiscountPercent[this] = value; }

    [DisplayName("Discount Amount Per Unit"), Size(18), Scale(2)]
    public decimal? DiscountAmountPerUnit { get => fields.DiscountAmountPerUnit[this]; set => fields.DiscountAmountPerUnit[this] = value; }

    [DisplayName("Net Discount Amount"), Size(18), Scale(2)]
    public decimal? NetDiscountAmount { get => fields.NetDiscountAmount[this]; set => fields.NetDiscountAmount[this] = value; }

    [DisplayName("Taxable Amount Per Unit"), Size(18), Scale(2), NotNull, NotMapped]
    public decimal? TaxableAmountPerUnit { get => fields.TaxableAmountPerUnit[this]; set => fields.TaxableAmountPerUnit[this] = value; }

    [DisplayName("Net Taxable Amount"), Size(18), Scale(2), NotMapped]
    public decimal? NetTaxableAmount { get => fields.NetTaxableAmount[this]; set => fields.NetTaxableAmount[this] = value; }

    [DisplayName("Gst Rate"), Column("GSTRateId"), ForeignKey(typeof(GstRatesRow)), LeftJoin(jGSTRate)]
    [TextualField(nameof(GSTRateRemarks)), ServiceLookupEditor(typeof(GstRatesRow), Service = "Default/GstRates/List")]
    public int? GSTRateId { get => fields.GSTRateId[this]; set => fields.GSTRateId[this] = value; }

    [DisplayName("Igst Rate"), Column("IGSTRate"), Size(18), Scale(2)]
    public decimal? IGSTRate { get => fields.IGSTRate[this]; set => fields.IGSTRate[this] = value; }

    [DisplayName("Igst Amount Per Unit"), Column("IGSTAmountPerUnit"), Size(18), Scale(2), NotMapped]
    public decimal? IGSTAmountPerUnit { get => fields.IGSTAmountPerUnit[this]; set => fields.IGSTAmountPerUnit[this] = value; }

    [DisplayName("Net Igst Amount"), Column("NetIGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? NetIGSTAmount { get => fields.NetIGSTAmount[this]; set => fields.NetIGSTAmount[this] = value; }

    [DisplayName("Cgst Rate"), Column("CGSTRate"), Size(18), Scale(2)]
    public decimal? CGSTRate { get => fields.CGSTRate[this]; set => fields.CGSTRate[this] = value; }

    [DisplayName("Cgst Amount Per Unit"), Column("CGSTAmountPerUnit"), Size(18), Scale(2), NotMapped]
    public decimal? CGSTAmountPerUnit { get => fields.CGSTAmountPerUnit[this]; set => fields.CGSTAmountPerUnit[this] = value; }

    [DisplayName("Net Cgst Amount"), Column("NetCGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? NetCGSTAmount { get => fields.NetCGSTAmount[this]; set => fields.NetCGSTAmount[this] = value; }

    [DisplayName("Sgst Rate"), Column("SGSTRate"), Size(18), Scale(2)]
    public decimal? SGSTRate { get => fields.SGSTRate[this]; set => fields.SGSTRate[this] = value; }

    [DisplayName("Sgst Amount Per Unit"), Column("SGSTAmountPerUnit"), Size(18), Scale(2), NotMapped]
    public decimal? SGSTAmountPerUnit { get => fields.SGSTAmountPerUnit[this]; set => fields.SGSTAmountPerUnit[this] = value; }

    [DisplayName("Net Sgst Amount"), Column("NetSGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? NetSGSTAmount { get => fields.NetSGSTAmount[this]; set => fields.NetSGSTAmount[this] = value; }

    [DisplayName("Dummy Field"), Size(200)]
    public string DummyField { get => fields.DummyField[this]; set => fields.DummyField[this] = value; }

    [DisplayName("Net Price Per Unit"), Size(18), Scale(2)]
    public decimal? NetPricePerUnit { get => fields.NetPricePerUnit[this]; set => fields.NetPricePerUnit[this] = value; }

    [DisplayName("Net Amount"), Size(18), Scale(2)]
    public decimal? NetAmount { get => fields.NetAmount[this]; set => fields.NetAmount[this] = value; }

    [DisplayName("Rejection Reason"), ForeignKey(typeof(RejectionReasonsRow)), LeftJoin(jRejectionReason)]
    [TextualField(nameof(RejectionReason))]
    [ServiceLookupEditor(typeof(RejectionReasonsRow), Service = "Default/RejectionReasons/List")]
    public int? RejectionReasonId { get => fields.RejectionReasonId[this]; set => fields.RejectionReasonId[this] = value; }

    [DisplayName("Assessment Remarks")]
    public string AssessmentRemarks { get => fields.AssessmentRemarks[this]; set => fields.AssessmentRemarks[this] = value; }

    [DisplayName("Replacement Method"), ForeignKey(typeof(ReplacementMethodsRow)), LeftJoin(jReplacementMethod)]
    [TextualField(nameof(ReplacementMethod))]
    [ServiceLookupEditor(typeof(ReplacementMethodsRow), Service = "Default/ReplacementMethods/List")]
    public int? ReplacementMethodId { get => fields.ReplacementMethodId[this]; set => fields.ReplacementMethodId[this] = value; }

    [DisplayName("Remarks")]
    public string Remarks { get => fields.Remarks[this]; set => fields.Remarks[this] = value; }

    [DisplayName("Credit Note No"), Origin(jCreditNote, nameof(CreditNotesRow.CreditNoteNo))]
    public string CreditNoteNo { get => fields.CreditNoteNo[this]; set => fields.CreditNoteNo[this] = value; }

    [DisplayName("Invoice Detail Commodity Description"), Origin(jInvoiceDetail, nameof(InvoiceDetailsRow.CommodityDescription))]
    public string InvoiceDetailCommodityDescription { get => fields.InvoiceDetailCommodityDescription[this]; set => fields.InvoiceDetailCommodityDescription[this] = value; }

    [DisplayName("Sales Return Detail Commodity Description")]
    [Origin(jSalesReturnDetail, nameof(SalesReturnDetailsRow.CommodityDescription))]
    public string SalesReturnDetailCommodityDescription { get => fields.SalesReturnDetailCommodityDescription[this]; set => fields.SalesReturnDetailCommodityDescription[this] = value; }

    //[DisplayName("Commodity Product Name"), Origin(jCommodity, nameof(ProductsRow.ProductName))]
    //public string CommodityProductName { get => fields.CommodityProductName[this]; set => fields.CommodityProductName[this] = value; }

    [DisplayName("Invoice Unit Name"), Origin(jInvoiceUnit, nameof(UnitsRow.UnitName)), LookupInclude]
    public string InvoiceUnitUnitName { get => fields.InvoiceUnitUnitName[this]; set => fields.InvoiceUnitUnitName[this] = value; }

    [DisplayName("Returned Unit Name"), Origin(jReturnedUnit, nameof(UnitsRow.UnitName)), LookupInclude]
    public string ReturnedUnitUnitName { get => fields.ReturnedUnitUnitName[this]; set => fields.ReturnedUnitUnitName[this] = value; }

    [DisplayName("GST Rate"), Origin(jGSTRate, nameof(GstRatesRow.Remarks)), LookupInclude]
    public string GSTRateRemarks { get => fields.GSTRateRemarks[this]; set => fields.GSTRateRemarks[this] = value; }

    [DisplayName("Rejection Reason"), Origin(jRejectionReason, nameof(RejectionReasonsRow.RejectionReason))]
    public string RejectionReason { get => fields.RejectionReason[this]; set => fields.RejectionReason[this] = value; }

    [DisplayName("Replacement Method"), Origin(jReplacementMethod, nameof(ReplacementMethodsRow.ReplacementMethod))]
    public string ReplacementMethod { get => fields.ReplacementMethod[this]; set => fields.ReplacementMethod[this] = value; }
}