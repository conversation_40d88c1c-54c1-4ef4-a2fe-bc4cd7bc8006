import { CustomersForm, CustomersRow, CustomersService, StatesRow } from '@/ServerTypes/Default';
import { Decorators, alertDialog, reloadLookup, reloadLookupAsync, toId } from '@serenity-is/corelib';
import { PendingChangesConfirmDialog } from '../../Common/Helpers/PendingChangesConfirmDialog';

@Decorators.registerClass('ReconnBooks.Default.CustomersDialog')
@Decorators.panel()
@Decorators.responsive()
export class CustomersDialog extends PendingChangesConfirmDialog<CustomersRow> {
    protected getFormKey() { return CustomersForm.formKey; }
    protected getRowDefinition() { return CustomersRow; }
    protected getService() { return CustomersService.baseUrl; }

    protected form = new CustomersForm(this.idPrefix);

    constructor() {
        super();

        this.form.GSTIN.element.on('input', () => {
            const input = this.form.GSTIN.value.toUpperCase().replace(/[\s-]/g, '');
            this.form.GSTIN.value = input === 'UNREGISTERED' ? 'UN-REGISTERED' : input;
            this.extractPlaceOfSupplyState();
            this.form.GSTIN.value = this.form.GSTIN.value.toUpperCase();
        });

        this.form.PAN.element.on('input', () => {
            this.form.PAN.value = this.form.PAN.value.toUpperCase();
        });

        this.form.IECNo.element.on('input', () => {
            this.form.IECNo.value = this.form.IECNo.value.toUpperCase();
        })

        this.form.UdyamNo.element.on('input', () => {
            this.form.UdyamNo.value = this.form.UdyamNo.value.toUpperCase();
        })
       
        this.form.BillingCityId.changeSelect2(async () => {
            this.form.BillingPinCode.value = undefined; 
        });

        this.form.MailingCityId.changeSelect2(async () => {
            this.form.MailingPINCode.value = undefined;
        });
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        if (this.isEditMode()) {
            this.extractPlaceOfSupplyState();
            this.setDialogsLoadedState();
        }
        this.setDialogsLoadedState();
    }

    private async extractPlaceOfSupplyState() {
        var stateCodeNo = this.form.GSTIN.value.substring(0, 2);
        var stateRow = (await StatesRow.getLookupAsync()).items.find(a => a.StateCodeNo === stateCodeNo);
        if (stateRow) {
            this.form.PlaceOfSupplyId.value = stateRow.StateId.toString();
        }
        else {
            this.form.PlaceOfSupplyId.value = null;
        }
    }
    protected updateInterface() {
        super.updateInterface();
        this.cloneButton.toggle(this.isEditMode());
    }
    
    protected getCloningEntity() {
        var clonedEntity = super.getCloningEntity();
        return clonedEntity;
    }

    protected validateBeforeSave(): boolean {
        const gstNumberPattern = /^[0-9]{2}.+$/;
        if (!(/^(Un-Registered)?$/i.test(this.form.GSTIN.value) || gstNumberPattern.test(this.form.GSTIN.value))) {
            alertDialog("Please enter as 'Un-Registered' in GSTIN field or enter a valid GSTIN number.");
            return false;
        }
        return super.validateBeforeSave();
    }

    protected updateTitle() {
        this.dialogTitle = "New Customer";
    }

    protected onSaveSuccess(response) {
        super.onSaveSuccess(response);
        reloadLookup(CustomersRow.lookupKey);
    }
}
