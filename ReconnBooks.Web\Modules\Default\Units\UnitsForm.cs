using Serenity.ComponentModel;

namespace ReconnBooks.Default.Forms;

[FormScript("Default.Units")]
[BasedOnRow(typeof(UnitsRow), CheckNames = true)]
public class UnitsForm
{
    [DisplayName("Unit Name")]
    public string UnitName { get; set; }

    [DisplayName("Unit Description")]
    public string UnitDescription { get; set; }
    public bool SetDefault { get; set; }

    [DisplayName("UQC Name")]
    public int UqcId { get; set; }
}