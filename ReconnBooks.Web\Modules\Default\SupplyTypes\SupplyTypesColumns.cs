using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.SupplyTypes")]
[BasedOnRow(typeof(SupplyTypesRow), CheckNames = true)]
public class SupplyTypesColumns
{
    public long RowNumber { get; set; }

    [EditLink, Width(300), Di<PERSON><PERSON><PERSON><PERSON>("SupplyType")]
    public string SupplyType { get; set; }

    [EditLink, Width(300), <PERSON><PERSON><PERSON><PERSON><PERSON>("NatureOfSupply")]
    public string NatureOfSupply { get; set; }

    [DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: false), AlignCenter]
    public int SupplyTypeId { get; set; }

    [Width(80), AlignCenter]
    public bool SetDefault { get; set; }
}