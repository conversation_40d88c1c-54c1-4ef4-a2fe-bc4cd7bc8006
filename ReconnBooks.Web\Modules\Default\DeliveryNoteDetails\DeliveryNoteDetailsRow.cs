using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("DeliveryNoteDetails")]
[DisplayN<PERSON>("Delivery Note Details"), InstanceN<PERSON>("Delivery Note Details"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
public sealed partial class DeliveryNoteDetailsRow : Row<DeliveryNoteDetailsRow.RowFields>, IIdRow, INameRow, IRowNumberedRow
{
    const string jDeliveryNote = nameof(jDeliveryNote);
    const string jCommodityType = nameof(jCommodityType);
    const string jCommodity = nameof(jCommodity);
    const string jHSNSACCode = nameof(jHSNSACCode);
    const string jUnit = nameof(jUnit);
    const string jGSTRate = nameof(jGSTRate);

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

    //-----------------------------------------------
    [DisplayName("Delivery Note Detail Id"), Identity, IdProperty, NameProperty]
    public int? DeliveryNoteDetailId { get => fields.DeliveryNoteDetailId[this]; set => fields.DeliveryNoteDetailId[this] = value; }

    [DisplayName("Delivery Note No."), NotNull, ForeignKey(typeof(DeliveryNotesRow)), LeftJoin(jDeliveryNote)]
    [TextualField(nameof(DeliveryNoteNo)), ServiceLookupEditor(typeof(DeliveryNotesRow), Service = "Default/DeliveryNotes/List")]
    public int? DeliveryNoteId { get => fields.DeliveryNoteId[this]; set => fields.DeliveryNoteId[this] = value; }

    [DisplayName("Delivery Note No."), Origin(jDeliveryNote, nameof(DeliveryNotesRow.DeliveryNoteNo))]
    public string DeliveryNoteNo { get => fields.DeliveryNoteNo[this]; set => fields.DeliveryNoteNo[this] = value; }

    [DisplayName("Commodity Type"), NotNull, ForeignKey(typeof(CommodityTypesRow)), LeftJoin(jCommodityType)]
    [TextualField(nameof(CommodityType))]
    [ServiceLookupEditor(typeof(CommodityTypesRow), Service = "Default/CommodityTypes/List")]
    public int? CommodityTypeId { get => fields.CommodityTypeId[this]; set => fields.CommodityTypeId[this] = value; }

    [DisplayName("Commodity Type"), Origin(jCommodityType, nameof(CommodityTypesRow.CommodityType)), LookupInclude]
    public string CommodityType { get => fields.CommodityType[this]; set => fields.CommodityType[this] = value; }

    [DisplayName("Commodity Name"), NotNull, ForeignKey(typeof(CommoditiesRow)), LeftJoin(jCommodity), TextualField(nameof(CommodityName))]
    [ServiceLookupEditor(typeof(CommoditiesRow), InplaceAdd = true, Service = "Default/Commodities/List", CascadeFrom = "CommodityTypeId", CascadeField = "CommodityTypeId")]
    [LookupInclude]
    public long? CommodityId { get => fields.CommodityId[this]; set => fields.CommodityId[this] = value; }

    [DisplayName("Product/Service Name"), Origin(jCommodity, nameof(CommoditiesRow.CommodityName)), LookupInclude]
    public string CommodityName { get => fields.CommodityName[this]; set => fields.CommodityName[this] = value; }

    //---- Fetching Product Code from Products Master -------------
    [DisplayName("Product Code")]
    [Origin(jCommodity, nameof(CommoditiesRow.CommodityCode)), TextualField(nameof(CommodityCode)), LookupInclude]
    [CommodityCodeEditor(CascadeFrom = "CommodityTypeId", CascadeField = "CommodityTypeId")]
    public string CommodityCode { get => fields.CommodityCode[this]; set => fields.CommodityCode[this] = value; }
    //--------------------------

    [DisplayName("Commodity Description"), QuickSearch]
    public string CommodityDescription { get => fields.CommodityDescription[this]; set => fields.CommodityDescription[this] = value; }

    [DisplayName("SKU"), Column("SKU"), Size(100)]  // SKU = Stock Keeping Unit
    public string Sku { get => fields.Sku[this]; set => fields.Sku[this] = value; }

    [DisplayName("HSN/SAC Code"), Origin(jCommodity, nameof(CommoditiesRow.HSNSACCodeId))]
    [ForeignKey(typeof(HsnsacCodesRow)), LeftJoin(jHSNSACCode)]
    [TextualField(nameof(HSNSACCode)), ServiceLookupEditor(typeof(HsnsacCodesRow), InplaceAdd = true, Service = "Default/HsnsacCodes/List")]
    public int? HSNSACCodeId { get => fields.HSNSACCodeId[this]; set => fields.HSNSACCodeId[this] = value; }

    [DisplayName("HSN/SAC Description")]
    [Origin(jHSNSACCode, nameof(HSNSACDescription)), LookupInclude]
    public string HSNSACDescription { get => fields.HSNSACDescription[this]; set => fields.HSNSACDescription[this] = value; }

    [DisplayName("HSN/SAC Group")]
    [Origin(jHSNSACCode, nameof(HSNSACGroup)), LookupInclude]
    public string HSNSACGroup { get => fields.HSNSACGroup[this]; set => fields.HSNSACGroup[this] = value; }

    [DisplayName("HSN/SAC Code")]
    [Origin(jHSNSACCode, nameof(HSNSACCode)), LookupInclude]
    public string HSNSACCode { get => fields.HSNSACCode[this]; set => fields.HSNSACCode[this] = value; }
    //------------------------------------------------------------

    [DisplayName("Quantity"), Size(18), Scale(2), NotNull]
    public decimal? Quantity { get => fields.Quantity[this]; set => fields.Quantity[this] = value; }

    [DisplayName("Unit of Measure"), NotNull, ForeignKey(typeof(UnitsRow)), LeftJoin(jUnit), TextualField(nameof(UnitName))]
    [ServiceLookupEditor(typeof(UnitsRow), InplaceAdd = true, Service = "Default/Units/List")]
    public int? UnitId { get => fields.UnitId[this]; set => fields.UnitId[this] = value; }

    [DisplayName("Unit of Measure"), Origin(jUnit, nameof(UnitsRow.UnitName)), LookupInclude]
    public string UnitName { get => fields.UnitName[this]; set => fields.UnitName[this] = value; }

    [DisplayName("Dummy Field1"), Size(10)]
    public string DummyField1 { get => fields.DummyField1[this]; set => fields.DummyField1[this] = value; }

    [DisplayName("Unit Price"), LookupInclude, Size(18), Scale(2), NotNull]
    public decimal? UnitPrice { get => fields.UnitPrice[this]; set => fields.UnitPrice[this] = value; }

    [DisplayName("Unit Amount"), Size(18), Scale(2), NotMapped]
    public decimal? UnitAmount { get => fields.UnitAmount[this]; set => fields.UnitAmount[this] = value; }

    //--------------------------
    [DisplayName("GST Rate (%)"), Column("GSTRateId"), NotNull, ForeignKey(typeof(GstRatesRow)), LeftJoin(jGSTRate)]
    [TextualField(nameof(GSTRateRemarks)), ServiceLookupEditor(typeof(GstRatesRow), Service = "Default/GstRates/List")]
    public int? GSTRateId { get => fields.GSTRateId[this]; set => fields.GSTRateId[this] = value; }

    [DisplayName("GST Rate"), Origin(jGSTRate, nameof(GstRatesRow.Remarks)), LookupInclude]
    public string GSTRateRemarks { get => fields.GSTRateRemarks[this]; set => fields.GSTRateRemarks[this] = value; }

    [DisplayName("IGST Rate (%)"), Column("IGSTRate"), Size(18), Scale(2)]
    public decimal? IGSTRate { get => fields.IGSTRate[this]; set => fields.IGSTRate[this] = value; }

    [DisplayName("IGST Amt./Unit"), Column("PerUnitIGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? PerUnitIGSTAmount { get => fields.PerUnitIGSTAmount[this]; set => fields.PerUnitIGSTAmount[this] = value; }

    [DisplayName("Net IGST Amt."), Column("IGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? IGSTAmount { get => fields.IGSTAmount[this]; set => fields.IGSTAmount[this] = value; }


    [DisplayName("CGST Rate (%)"), Column("CGSTRate"), Size(18), Scale(2)]
    public decimal? CGSTRate { get => fields.CGSTRate[this]; set => fields.CGSTRate[this] = value; }

    [DisplayName("CGST Amt./Unit"), Column("PerUnitCGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? PerUnitCGSTAmount { get => fields.PerUnitCGSTAmount[this]; set => fields.PerUnitCGSTAmount[this] = value; }

    [DisplayName("Net CGST Amt."), Column("CGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? CGSTAmount { get => fields.CGSTAmount[this]; set => fields.CGSTAmount[this] = value; }

    [DisplayName("SGST Rate"), Column("SGSTRate"), Size(18), Scale(2)]
    public decimal? SGSTRate { get => fields.SGSTRate[this]; set => fields.SGSTRate[this] = value; }

    [DisplayName("SGST Amt./Unit"), Column("PerUnitSGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? PerUnitSGSTAmount { get => fields.PerUnitSGSTAmount[this]; set => fields.PerUnitSGSTAmount[this] = value; }

    [DisplayName("Net SGST Amt."), Column("SGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? SGSTAmount { get => fields.SGSTAmount[this]; set => fields.SGSTAmount[this] = value; }

    //-----------------------------------------------
    [DisplayName("Dummy Field"), Size(200)]
    public string DummyField { get => fields.DummyField[this]; set => fields.DummyField[this] = value; }

    [DisplayName("Price Per Unit"), Size(18), Scale(2), NotMapped]
    public decimal? PerUnitPrice { get => fields.PerUnitPrice[this]; set => fields.PerUnitPrice[this] = value; }

    [DisplayName("Net Amount"), Size(18), Scale(2)]
    public decimal? NetAmount { get => fields.NetAmount[this]; set => fields.NetAmount[this] = value; }




}