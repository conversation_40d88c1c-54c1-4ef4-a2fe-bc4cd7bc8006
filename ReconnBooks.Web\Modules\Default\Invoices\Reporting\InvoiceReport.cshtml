@using ReconnBooks.Modules.Common.Helpers;
@model ReconnBooks.Modules.Default.Invoices.Reporting.InvoiceReportData
@{
    Layout = "";
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <!-- Include Bootstrap CSS from CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0px 0px 10px 15px; /* top right bottom left */
            padding: 0px;
            font-family: Calibri, sans-serif;
            position: relative;
        }

        h1, h2, h3 {
            margin-top: 0 !important;
        }

        .header-table,
        .invoice-table,
        .Customer-table,
        .Product-table,
        .HeaderNote-table,
        .FooterNote-table {
            width: 100%; /* Ensures the table takes full width */
            border-collapse: collapse;
            margin-bottom: 0px;
        }

        .header-table td,
        .invoice-table td,
        .Customer-table td {

            vertical-align: middle;
        }

        .qr-code {
            width: 100%; /* Fixed width for both logo and QR code */
            height: auto; /* Maintain aspect ratio */
        }

        .logo {
            max-width: 100%; /* Take full width available */
            height: auto; /* Maintain aspect ratio */
        }

        .address-container {
            text-align: left;
            word-wrap: break-word; /* Ensure text wraps within column */
        }

        .address-container strong {
            font-size: 25px;
        }

        /* Invoice Table Styling */
        .invoice-table td {
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            padding: 2px;
            background-color: #f0f0f0 !important;
            border-bottom: none !important;
            border: 1px solid black!important;
            position: relative;
        }

        .report-type {
            color: black;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 17px;
            font-weight: normal;
            background-color: #f0f0f0 !important;
        }

        .Product-table {
            width: 100%;
            table-layout: fixed; /* Ensures fixed column widths */
            border-collapse: collapse;
            margin-top: 0px;
            border: 0.25px solid black;
        }

        .Product-table td {
            padding: 2px;
            text-align: left;
            border-left: 0.25px solid black; /* Vertical lines only */
            vertical-align: top !important; /* Align header content at the top */
            line-height: 1.1 !important;
        }

        .Product-table th {
            padding: 2px;
            text-align: center;
            border-left: 0.25px solid black; /* Vertical lines only */
            font-size: 16px;
            height: 40px !important;
            line-height: 1.1 !important;
            vertical-align: central; /* Align header content at the top */
        }

        .Product-table th:first-child,
        .Product-table td:first-child {
            border-left: none; /* Remove the first vertical border */
        }

        .Product-table td {
            border-top: none; /* Remove horizontal lines */
            border-bottom: none; /* Remove horizontal lines */
        }

        .Product-table th,
        .totals-row td {
            background-color: #f0f0f0;
            vertical-align: central; /* Align header content at the top */
        }

        .Product-table td, .Product-table th {
            word-wrap: break-word; /* Break long words */
            word-break: break-word; /* Break long words */
            white-space: normal; /* Allow wrapping */
        }

        .summary-table {
            width: 100%;
            margin-top: 0px;
            /* border: px solid black; /* Adds a border around the entire table */
            border-collapse: collapse; /* Ensures a clean border appearance */
            border-top: none;
        }

        .summary-table td {
            padding: 8px;
            vertical-align: top;
            font-size: 15px;
            border-top: none !important;
        }

        .summary-right {
            width: 39px; /* Retains the width of the right section */
            text-align: right; /* Keeps the text right-aligned */
        }

        .summary-right .entry {
            display: flex; /* Flex for better control */
            justify-content: flex-end; /* Aligns items to the right */
            margin-bottom: 8px; /* Adjusts space between entries */
        }

        .summary-right .label {
            display: inline-block;
            width: 150px; /* Adjusted label width */
            text-align: right; /* Aligns label to the right */
        }

        .summary-right .colon {
            display: inline-block;
            width: 10px; /* Adjusts the space between label and value */
            text-align: center; /* Keeps colon centered */
        }

        .summary-right .value {
            display: inline-block;
            width: 140px; /* Adjusted value width to 25% */
            text-align: right; /* Ensures value is aligned to the right */
            padding-left: 3px; /* Space between colon and value */
        }

        .summary-right .label,
        .summary-right .colon,
        .summary-right .value {
            font-size: 18px !important;
        }

        .summary-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 0; /* Remove gap to align both sections */
            border-bottom: 0.25px solid black !important;
            border: 0.25px solid black; /* Light gray outer border */
            padding: 0px; /* No padding to align contents perfectly */
        }

        .summary-table {
            border-collapse: collapse;
            width: 80%; /* Adjust width as needed */
            border-right: none; /* Remove the right border to merge with the separator */
        }

        .summary-right {
            padding: 8px; /* Add space to content inside the right section */
        }

        .summary-right {
            width: 39%; /* Adjust width as needed */
            border-left: 0.25px solid black; /* Separator line between the two sections */
            padding-left: 0px; /* Add space to content inside the right section */
        }

        .summary-table th, .summary-table td {
            border-top: none;
            border-bottom: 0.25px solid black; 
            border-right: 0.25px solid black; 
            padding: 8px;
        }

        .summary-table td.total-value {
            border-right: 0.25px solid black;
        }

        .summary-table th {
            text-align: center !important;
        }

        .entry {
            margin-bottom: 10px;
        }

        .entry .colon {
            margin: 0 5px;
        }

        .entry .value {
            font-weight: normal;
        }

        .entry.single-line {
            white-space: nowrap; /* Prevent line breaks */
            overflow: hidden; /* Hide overflowing content */
            text-overflow: ellipsis; /* Add ellipsis for overflowed content */
        }

        /* Customer Table Styling */
        .Customer-table {
            border: 0.25px solid black;
            border-collapse: collapse;
            width: 100%;
        }

        /* Container for Customer Info and Shipping Address */
        .customer-info-container {
            position: relative;
            padding-bottom: 0px;
            padding-top: 0px;
            margin-top: -20px;
        }

        /* Customer Info */
        .customer-info {
            vertical-align: top;
            padding-left: 3px;
            width: 100%;
        }

        /* Shipping Address Table */
        .shipping-address-table {
            position: relative;
            width: 100%;
            margin-top: 5px; /* Moves the border closer to GST No */
            border-top: 0.25px solid black; /* Increased thickness */
            border-bottom: none !important;
            border-left: none !important;
            border-right: none !important;
        }


        /* Invoice Info and Date Styling */
        .invoice-info,
        .invoiceNo-info,
        .date-info {
            text-align: left;
            border: 0.25px solid black;
            padding: 1px 0px;
            line-height: 1;
            font-size: 17px;
            height: 30px; /* Set a fixed height */
            overflow: hidden; /* Prevents text from increasing row height */
            vertical-align: middle; /* Ensures text stays centered */
        }

        .invoice-info {
            width: 15%;
            padding: 0px;
        }

        .invoiceNo-info {
            width: 19%;
            padding: 0px;
        }

        .date-info {
            width: 9%;
            padding-right: 3px !important;
        }

         /* Entry line styling for colon alignment */
        .entry-line {
            display: table;
            width: 100%;
            font-size: 17px;
            line-height: 1.5;
            margin-bottom: 3px;
        }

        .entry-line .label,
        .entry-line .colon,
        .entry-line .value {
            display: table-cell;
            vertical-align: top;
        }

        .entry-line .label {
            width: 130px;
            white-space: nowrap;
            padding-right: 5px;
        }

        .entry-line .colon {
            width: 10px;
            text-align: center;
            padding-right: 5px;
        }

        .entry-line .value {
            word-break: break-word;
            white-space: pre-wrap;
        }

        .Customer-table td strong {
            font-weight: bold;
        }

        /* Payment terms padding */
        .Payment-info {
            padding-left: 10px;
        }

        .header-table {
            table-layout: fixed;
        }

        .footer-note {
            margin-top: 0px;
            padding-right: 10px;
            padding: 8px;
        }

        /* Container for the bank details and QR code */
        .bank-details-container {
            display: flex; /* Display as a flexible container */
            gap: 20px; /* Add a gap between the bank details and QR code */
            align-items: flex-start; /* Align items to the top of the container */
            font-size: 17px; /* Set the font size for the container */
        }

        /* Styles for the bank details text */
        .bank-details-text {
            flex: 1; /* Let the text section take up available space */
            display: inline-block; /* Display the text as an inline block */
            vertical-align: top; /* Align the text to the top of the container */
        }

        /* Styles for the bank QR code */
        .bank-qr-code {
            flex-shrink: 0; /* Prevent the QR code from shrinking */
            width: 120px; /* Set the width of the QR code */
            height: 120px; /* Set the height of the QR code */
            display: flex; /* Display the QR code as a flexible container */
            justify-content: center; /* Center the content horizontally */
            align-items: center; /* Center the content vertically */
            text-align: center; /* Align text or image in the center */
            box-sizing: border-box; /* Include padding and border in size calculations */
        }

        /* Styles for the QR code image */
        .bank-qr-code img {
            max-width: 100%; /* Ensure the image fits within the QR code container */
            max-height: 100%; /* Ensure the image fits within the QR code container */
            object-fit: contain; /* Maintain the aspect ratio of the QR code image */
        }

        .Product-table td:first-child,
        .Product-table th:first-child {
            border-right: none; /* Remove right border of the first column */
        }

        .Product-table td:nth-child(2),
        .Product-table th:nth-child(2) {
            border-left: none; /* Remove left border of the second column */
        }

        .header-table {
            width: 100vw;
            margin-left: -20px;
            margin-right: -20px;
            border-collapse: collapse;
        }

        .header-table td {
            padding: 2px !important;
            vertical-align: middle;
        }

        thead tr th {
            line-height: 35px !important;
        }

        /* Dynamic height row that will expand to fill available space */
        .dynamic-height-row {
            height: 50px; /* Default height that will be multiplied by the number of rows */
        }

        /* Ensure the dynamic row cells maintain proper width */
        .dynamic-height-row td {
            border-left: 0.25px solid black; /* Match the border style of other cells */
            border-right: none;
            border-top: none;
            border-bottom: none;
        }

        /* First cell in dynamic row */
        .dynamic-height-row td:first-child {
            border-left: none;
        }

        /* Remove flexbox from tables to preserve column widths */
        .Product-table {
            /* Keep table layout as is without flexbox */
            table-layout: fixed; /* Ensure fixed column widths */
            width: 100%;
        }

        .Product-table tbody {
            /* Standard table display */
            display: table-row-group;
        }

        .container-fluid {
            min-height: 100%;
            height: auto;
            width: 100%;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header Table -->
        <table class="header-table">
            <tr>
                <!-- Logo Column -->
                <td class="col-sm-2 text-center">
                    <img src="/upload/@Model.Client.Logo" alt="" class="logo" style="@(string.IsNullOrWhiteSpace(Model.Client.Logo) ? "display: none;" : "")" />
                </td>

                <!-- Address Column -->
                <td class="col-sm-9 address-container" style="line-height: 1.3;">
                    <div style="font-size: 27px; text-align: center;"><strong>@Model.Client.ClientName</strong></div>
                    <div style="font-size: 17px; text-align: center;">
                        <div style="font-weight: bold;">
                            @Model.Client.Address, @Model.Client.CityName - @Model.Client.PINCode<br />
                        </div>

                        <div style="font-weight: bold;">
                            @(string.Join(" | ", new[] {
                            Model.Client.GSTIN != null ? $"GSTIN: {Model.Client.GSTIN}" : null,
                            Model.Client.PlaceOfSupplyStateCode != null ? $"{Model.Client.PlaceOfSupplyStateCode} - {Model.Client.PlaceOfSupplyStateCodeNo}" : null,
                            Model.Client.PAN != null ? $"PAN: {Model.Client.PAN}" : null,
                            Model.Client.UdyamNo != null ? $"UDYAM No.: {Model.Client.UdyamNo}" : null
                            }.Where(x => x != null)))<br />
                        </div>

                        @(string.Join(" | ", new[] {
                        Model.Client.HomePage,
                        Model.Client.EMail != null ? $"Email: {Model.Client.EMail}" : null,
                        Model.Client.PhoneNo != null ? $"Phone: {Model.Client.PhoneNo}" : (Model.Client.MobileNo != null ? $"Phone: {Model.Client.MobileNo}" : null)
                        }.Where(x => x != null)))<br />

                        @(string.Join(" | ", new[] {
                        Model.Client.CINNo != null ? $"CIN: {Model.Client.CINNo}" : null,
                        Model.Client.IECNo != null ? $"IECNo: {Model.Client.IECNo}" : null
                        }.Where(x => x != null)))
                    </div>

                    <div style="font-size: 17px!important; text-align: center; font-weight: bold;">
                        @(Model.Client.TagLine != null ? Model.Client.TagLine : string.Empty)
                    </div>
                </td>

                <!-- QR Code Column -->
                <td class="col-sm-2 text-center">
                    <img src="/upload/@Model.Invoice.EInvoiceQRCode" alt="" class="qr-code" style="@(string.IsNullOrWhiteSpace(Model.Invoice.EInvoiceQRCode) ? "display: none;" : "")" />
                </td>
            </tr>
        </table>

        <!-- invoice Table -->
        <table class="table invoice-table" style="width: 100%;">
            <tr>
                <td colspan="3" style="text-align: center; position: relative;">
                    TAX INVOICE
                    <span class="report-type">@Model.ReportType</span>
                </td>
            </tr>
        </table>

        <!-- Customer Table -->
        <table class="Customer-table" style="border: 0.25px solid black; border-collapse: collapse; width: 100%;">
            <tr>
                <!-- Customer Info and Shipping Address Container -->
                <td class="customer-info-container" rowspan="7" style="vertical-align: top; width: 48%; border-right: 0.25px solid black;">
                    <div class="customer-info" style="font-size: 18px; padding-left: 3px; margin: 0; width: 100%; box-sizing: border-box;">
                        <span>Customer Name:</span><br>
                        @if (@Model.Customer.AddressedTo != null)
                        {
                            <span>@Model.Customer.AddressedTo</span>
                            <br>
                        }
                        <span><strong>@Model.Customer.CompanyName</strong></span><br>
                        <span>
                            @Model.Customer.BillingAddress<br />
                            @Model.Customer.BillingCityCityName - @Model.Customer.BillingPinCode
                        </span><br>
                        @if (!string.IsNullOrWhiteSpace(Model.Customer.GSTIN) || !string.IsNullOrWhiteSpace(Model.Customer.PAN) || !string.IsNullOrWhiteSpace(Model.Customer.UdyamNo))
                            {
                                <span style="display: block; margin-bottom: 10px;">
                                    <strong>GST No.:@Model.Customer.GSTIN</strong>,
                                    @Model.Customer.PlaceOfSupplyStateName 
                                    @Html.Raw(string.IsNullOrWhiteSpace(Model.Customer.PAN) ? "" : $" | PAN: {Model.Customer.PAN}")<br />
                                    @Html.Raw(string.IsNullOrWhiteSpace(Model.Customer.UdyamNo) ? "" : $" UDYAM No.: {Model.Customer.UdyamNo}")
                                </span>
                            }
                        @if (Model.Invoice.ShippingAddress != null || Model.Invoice.ShipToCustomerName != null || Model.Invoice.ShippingCityName != null || Model.Invoice.ShippingPinCode != null || Model.Invoice.ShippingGSTIN != null)

                        {
                            <span style="display: block; margin-top: 10px; text-decoration-line: underline;">Shipping Address:</span>
                            <span style="display: block;">
                                @Html.Raw(Model.Invoice.ShipToCustomerName != null ? $"<strong>{Model.Invoice.ShipToCustomerName}</strong><br />" : string.Empty)
                                @(Model.Invoice.ShippingAddress != null ? $"{Model.Invoice.ShippingAddress}" : string.Empty)<br />
                                @(Model.Invoice.ShippingCityName != null ? $"{Model.Invoice.ShippingCityName}" : string.Empty)
                                @(Model.Invoice.ShippingPinCode != null ? $" - {Model.Invoice.ShippingPinCode}" : string.Empty)
                                @if (Model.Invoice.ShippingCityName != null || Model.Invoice.ShippingPinCode != null)

                                {
                                    <br />
                                }
                                @(Model.Invoice.ShippingGSTIN != null ? $"GST No.: {Model.Invoice.ShippingGSTIN}" : string.Empty)
                                @(Model.Invoice.ShippingPlaceOfSupplyStateName != null ? $", {Model.Invoice.ShippingPlaceOfSupplyStateName}" : string.Empty)
                            </span>
                        }

                    </div>

                <!-- Invoice Info -->
                <td class="invoice-info" style="font-size: 17px; padding: 3px; padding-left: 3px; font-weight: bold;">
                    Invoice No.
                </td>
                <td class="invoiceNo-info" style="font-size: 17px; padding: 3px; padding-left: 3px; border-right: none; font-weight: bold;">
                    @Model.Invoice.InvoiceNo
                </td>
                <td class="date-info" style="font-size: 17px; padding-left: 13px; padding-right: 3px; border-left: none; font-weight: bold;">
                    @Model.Invoice.InvoiceDate.IndianFormatDate()
                </td>
            </tr>
            <tr>
                <td class="invoice-info" style="font-size: 17px; padding: 3px; padding-left: 3px;">
                    Order Reference No.
                </td>
                <td class="invoiceNo-info" style="font-size: 17px; padding: 3px; padding-left: 3px; border-right: none;">
                    @(Model.Invoice.OrderRefNo ?? string.Empty)
                </td>
                <td class="date-info" style="font-size: 17px; padding-left: 13px; padding-right: 3px; border-left: none;">
                    @(Model.Invoice?.OrderRefDate.IndianFormatDate() ?? string.Empty)
                </td>
            </tr>
            <tr>
                <td class="invoice-info" style="font-size: 17px; padding: 3px; padding-left: 3px;">
                    Delivery Note No.
                </td>
                <td class="invoiceNo-info" style="font-size: 17px; padding: 3px; padding-left: 3px; border-right: none;">
                    @(Model.Invoice.DeliveryNoteNo ?? string.Empty)
                </td>
                <td class="date-info" style="font-size: 17px; padding-left: 13px; padding-right: 3px; border-left: none;">
                    @(Model.DeliveryNote?.DeliveryNoteDate.IndianFormatDate() ?? string.Empty)
                </td>
            </tr>
            <tr>
                <td class="invoice-info" style="font-size: 17px; padding: 3px; padding-left: 3px;">
                    E-way Bill No.
                </td>
                <td class="invoiceNo-info" style="font-size: 17px; padding: 3px; padding-left: 3px; border-right: none;">
                    @Model.Invoice.EWayBillNo
                </td>
                <td class="date-info" style="font-size: 17px; padding-left: 13px; padding-right: 3px; border-left: none;">
                    @Model.Invoice.EWayBillDate.IndianFormatDate()
                </td>
            </tr>
            <tr>
                <td class="invoice-info" style="font-size: 17px; padding: 3px; padding-left: 3px;">
                    Acknowledgement No.
                </td>
                <td class="invoiceNo-info" style="font-size: 17px; padding: 3px; padding-left: 3px; border-right: none;">
                    @Model.Invoice.AcknowledgementNo
                </td>
                <td class="date-info" style="font-size: 17px; padding-left: 13px; padding-right: 3px; border-left: none;">
                    @Model.Invoice.AcknowledgementDate.IndianFormatDate()
                </td>
            </tr>
            <tr>
                <!-- New row for Shipping Address, Docket No., Vehicle No., Payment Terms, and Payment Due Date -->
            <tr>
                <td class="shipping-info" colspan="4" style="vertical-align: top; padding-left: 3px!important; line-height: 1.5;">
                    @if (Model.Invoice.IRN != null)
                        {
                            <div style="font-size: 17px; line-height: 1.2; white-space: normal; word-break: break-word; text-indent: -40px; padding-left: 40px;">
                                <span style="display: inline;">IRN&nbsp;:&nbsp;@Model.Invoice.IRN</span>
                            </div>
                        }
                               
                    <!-- Shipped Via. -->
                    @if (Model.Invoice.ShippedVia != null)
                        {
                            <div class="entry-line" style="display: flex; align-items: flex-start;">
                                <span class="label">Shipped Via.</span>
                                <span class="colon">:</span>
                                <span class="value">@Model.Invoice.ShippedVia</span>
                            </div>
                        }
                    <!-- Docket No. -->
                     @if (Model.Invoice.ShippingDocketNo != null)
                        {
                            <div class="entry-line" style="display: flex; align-items: flex-start;">
                                <span class="label">Docket No.</span>
                                <span class="colon">:</span>
                                <span class="value">@Model.Invoice.ShippingDocketNo</span>
                            </div>
                        }
                    <!-- Vehicle No. -->
                        @if (Model.Invoice.VehicleNo != null)
                        {
                            <div class="entry-line" style="display: flex; align-items: flex-start;">
                                <span class="label">Vehicle No.</span>
                                <span class="colon">:</span>
                                <span class="value">@Model.Invoice.VehicleNo</span>
                            </div>
                        }
                    <!-- Payment Terms -->
                     @if (Model.Invoice.PaymentTerms != null)
                        {
                            <div class="entry-line">
                                <span class="label">Payment Terms</span>
                                <span class="colon">:</span>
                                <span class="value" style="line-height: 1.2 !important;">@Model.Invoice.PaymentTerms</span>
                            </div>
                        }
                    <!-- Payment Due Date -->
                     @if (Model.Invoice.PaymentDueDate != null)
                        {
                            <div class="entry-line" style="display: flex; align-items: flex-start;">
                                <span class="label">Payment Due Date</span>
                                <span class="colon">:</span>
                                <span class="value">@Model.Invoice.PaymentDueDate.IndianFormatDate()</span>
                            </div>
                        }
                </td>
            </tr>
        </table>

        <table class="Product-table">
            <colgroup>
                <col style="width: 3px;"> <!-- Sl. No. -->
                <col style="width:@(Model.Customer.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId
                ? (Model.Invoice.InvoiceDetailsList.Any(x => x.DiscountPercent > 0) ? "23px" : "32px")
                : (Model.Invoice.InvoiceDetailsList.Any(x => x.DiscountPercent > 0) ? "30px" : "39px"));"> <!-- Description -->
                <col style="width: 8px;"> <!-- Qty Unit -->
                <col style="width: 9px;"> <!-- Unit Price -->
                @if (Model.Invoice.InvoiceDetailsList.Any(x => x.DiscountPercent > 0))
                {
                    <col style="width: 7px;"> <!-- Discount Amt. -->
                }

                <col style="width: 10px;"> <!-- Taxable Amt. -->
                @if (Model.Customer.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId)
                {
                    <col style="width: 9px;"> <!-- CGST Amt. -->
                    <col style="width: 9px;"> <!-- SGST Amt. -->
                }
                else
                {
                    <col style="width: 9px;"> <!-- IGST Amt. -->
                }

                <col style="width: 10px;"> <!-- Net Price/Unit -->
                <col style="width: 12px;"> <!-- Net Amt. -->
            </colgroup>

            <thead>
                <tr>
                    <th></th>
                    <th>Product/Service Description</th>
                    <th>Qty.</th>
                    <th>Unit Price</th>

                    @if (Model.Invoice.InvoiceDetailsList.Any(x => x.DiscountPercent > 0))
                    {
                        <th>Discount Amt.</th>
                    }

                    <th>Taxable Amt.</th>

                    @if (Model.Customer.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId)
                    {
                        <th>CGST Amt.</th>
                        <th>SGST Amt.</th>
                    }
                    else
                    {
                        <th>IGST Amt.</th>
                    }
                    <th>Net Unit Price</th>
                    <th>Net Amt.</th>
                </tr>
            </thead>

            @{
                var counter = 1;
                var isIntraState = Model.Customer.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId;
                var hasDiscount = Model.Invoice.InvoiceDetailsList.Any(x => x.DiscountPercent > 0);
            }

            @foreach (var invoiceDetail in Model.Invoice.InvoiceDetailsList)
            {
                <tr>
                    <td style="font-size: 17px; text-align: center">@(counter++.ToString("#,##0"))</td>
                    <td style="width: 90%; text-align: left; font-size: 18px; line-height: 1.1;">
                        <strong>@invoiceDetail.CommodityName</strong>
                        @if (!string.IsNullOrEmpty(invoiceDetail.CommodityDescription))
                        {
                            <br/>
                            <span style="font-size: 16px; white-space: pre-wrap;">@invoiceDetail.CommodityDescription</span>
                        }
                        @if (!string.IsNullOrEmpty(invoiceDetail.CommodityCode))
                        {
                            <br/>
                            <span style="font-size: 16px;">Part No.: @invoiceDetail.CommodityCode</span>
                        }
                        @if (!string.IsNullOrEmpty(invoiceDetail.HSNSACCode))
                        {
                            <br/>
                            <span style="font-size: 16px;">HSN Code: @invoiceDetail.HSNSACCode</span>
                        }
                        @if (!string.IsNullOrEmpty(invoiceDetail.SKU))
                        {
                            <br />
                            <span style="font-size: 16px;">SKU: @invoiceDetail.SKU</span>
                        }
                        @if (!string.IsNullOrEmpty(invoiceDetail.ProductSerialNos))
                        {
                            <br />
                            <span style="font-size: 16px; font-style: italic;">Sl Nos: @invoiceDetail.ProductSerialNos</span>
                        }

                    </td>
                    <td style="text-align: center; font-size: 17px">
                        @invoiceDetail.Quantity?.ToString("#,##0.##") @invoiceDetail.UnitName
                    </td>
                    <td style="text-align: right; font-size: 17px">
                        @(invoiceDetail.UnitPrice?.IndianFormat() ?? string.Empty)
                    </td>

                    @if (hasDiscount)
                    {
                        <td style="text-align: right; font-size: 17px">
                            @(invoiceDetail.NetDiscountAmount?.IndianFormat() ?? string.Empty)<br />
                            <span style="font-size: 15px;">@(invoiceDetail.DiscountPercent > 0 ? $"{invoiceDetail.DiscountPercent?.ToString("#0.##")} %" : string.Empty)</span>
                        </td>
                    }

                    <td style="text-align: right; font-size: 17px">
                        @(invoiceDetail.NetTaxableAmount?.IndianFormat() ?? string.Empty)
                    </td>

                    @if (isIntraState)
                    {
                        <td style="text-align: right; font-size: 17px">
                            @(invoiceDetail.NetCGSTAmount?.IndianFormat() ?? "0")<br />
                            <span style="font-size: 15px;">@($"{invoiceDetail.CGSTRate?.ToString("#0.##")}%" ?? string.Empty)</span>
                        </td>
                        <td style="text-align: right; font-size: 17px">
                            @(invoiceDetail.NetSGSTAmount?.IndianFormat() ?? "0")<br />
                            <span style="font-size: 15px;">@($"{invoiceDetail.SGSTRate?.ToString("#0.##")}%" ?? string.Empty)</span>
                        </td>
                    }
                    else
                    {
                        <td style="text-align: right; font-size: 17px">
                            @(invoiceDetail.NetIGSTAmount?.IndianFormat() ?? "0")<br />
                            <span style="font-size: 15px;">@($"{invoiceDetail.IGSTRate?.ToString("#0.##")}%" ?? string.Empty)</span>
                        </td>
                    }

                    <td style="text-align: right; font-size: 17px">
                        @(invoiceDetail.NetPricePerUnit?.IndianFormat() ?? "0")
                    </td>
                    <td style="text-align: right; font-size: 17px">
                        <strong>@(invoiceDetail.NetAmount?.IndianFormat() ?? string.Empty)</strong>
                    </td>
                </tr>
            }

            <!-- Added dummy row with empty cells -->
            <!-- Dynamic row that will expand to fill available space -->
            @{
                // Calculate remaining space to fill the page
                var itemCount = Model.Invoice.InvoiceDetailsList.Count;
                // Adjust min rows based on content - fewer items means more space needed
                var minRows = Math.Max(1, 10 - itemCount);
            }
            @for (int i = 0; i < minRows; i++)
            {
                <tr class="dynamic-height-row">
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    @if (hasDiscount)
                    {
                        <td></td>
                    }
                    <td></td>
                    @if (isIntraState)
                    {
                        <td></td>
                        <td></td>
                    }
                    else
                    {
                        <td></td>
                    }
                    <td></td>
                    <td></td>
                </tr>
            }

            <tfoot>
                @{
                    int columnCount = isIntraState ? (hasDiscount ? 10 : 9) : (hasDiscount ? 9 : 8);
                }
                <tr style="line-height: 1px; height: 1px;">
                    @for (int i = 0; i < columnCount; i++)
                    {
                        <td style="font-size: 17px"></td>
                    }
                </tr>
            </tfoot>
        </table>

        <table class="Product-table" style="margin-top: 0px!important; border-top: none!important;height:35px;">
            <colgroup>
                <col style="width: 3px;"> <!-- Sl. No. -->
                <col style="width:@(Model.Customer.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId
                ? (Model.Invoice.InvoiceDetailsList.Any(x => x.DiscountPercent > 0) ? "23px" : "32px")
                : (Model.Invoice.InvoiceDetailsList.Any(x => x.DiscountPercent > 0) ? "30px" : "39px"));"> <!-- Description -->
                <col style="width: 8px;"> <!-- Qty Unit -->
                <col style="width: 9px;"> <!-- Unit Price -->
                @if (Model.Invoice.InvoiceDetailsList.Any(x => x.DiscountPercent > 0))
                {
                    <col style="width: 7px;"> <!-- Discount Amt. -->
                }

                <col style="width: 10px;"> <!-- Taxable Amt. -->
                @if (Model.Customer.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId)
                {
                    <col style="width: 9px;"> <!-- CGST Amt. -->
                    <col style="width: 9px;"> <!-- SGST Amt. -->
                }
                else
                {
                    <col style="width: 9px;"> <!-- IGST Amt. -->
                }

                <col style="width: 10px;"> <!-- Net Price/Unit -->
                <col style="width: 12px;"> <!-- Net Amt. -->
            </colgroup>

            <tbody>
                <tr class="totals-row">
                    <td style="font-size: 17px"></td>
                    <td style="font-size: 17px; text-align: right; vertical-align: middle !important">Total</td>
                    <td style="font-size: 17px"></td>
                    <td style="font-size: 17px"></td>

                    @if (Model.Invoice.InvoiceDetailsList.Any(x => x.DiscountPercent > 0))
                    {
                        <td style="text-align: right; font-size: 17px; vertical-align: middle !important">
                            @Model.Invoice.InvoiceDetailsList.Sum(d => d.NetDiscountAmount ?? 0).IndianFormat()
                        </td>
                    }

                    <td style="text-align: right; font-size: 17px; vertical-align: middle !important">
                        @Model.Invoice.InvoiceDetailsList.Sum(d => d.NetTaxableAmount ?? 0).IndianFormat()
                    </td>

                    @if (Model.Customer.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId)
                    {
                        <td style="text-align: right; font-size: 17px; vertical-align: middle !important">
                            @Model.Invoice.InvoiceDetailsList.Sum(d => d.NetCGSTAmount ?? 0).IndianFormat()
                        </td>
                        <td style="text-align: right; font-size: 17px; vertical-align: middle !important">
                            @Model.Invoice.InvoiceDetailsList.Sum(d => d.NetSGSTAmount ?? 0).IndianFormat()
                        </td>
                    }
                    else
                    {
                        <td style="text-align: right; font-size: 17px;vertical-align: middle !important">
                            @Model.Invoice.InvoiceDetailsList.Sum(d => d.NetIGSTAmount ?? 0).IndianFormat()
                        </td>
                    }

                    <td style="text-align: right; font-size: 17px;vertical-align: middle !important">
                        @Model.Invoice.InvoiceDetailsList.Sum(d => d.NetPricePerUnit ?? 0).IndianFormat()
                    </td>
                    <td style="text-align: right; font-size: 17px;vertical-align: middle !important">
                        <strong>@Model.Invoice.InvoiceDetailsList.Sum(d => d.NetAmount ?? 0).IndianFormat()</strong>
                    </td>
                </tr>
            </tbody>
        </table>

        <div class="summary-container">

        <!-- Left Table -->
        <table class="summary-table">
            <tr>
                <th>HSN/SAC</th>
                <th>Taxable Value</th>

                @if (Model.Customer.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId)
                {
                    <th>CGST %</th>
                    <th>CGST Amt.</th>
                    <th>SGST %</th>
                    <th>SGST Amt.</th>
                }
                else
                {
                    <th>IGST %</th>
                    <th>IGST Amt.</th>
                }

                <th>Total Value</th>
            </tr>

            @{
                var groupedInvoiceDetails = Model.Invoice.InvoiceDetailsList.GroupBy(x => x.HSNSACCode);

                foreach (var group in groupedInvoiceDetails)
                {
                    var invoiceDetail = group.First();
                    var sumNetTaxableAmount = group.Sum(x => x.NetTaxableAmount ?? 0);
                    var sumNetAmount = group.Sum(x => x.NetAmount ?? 0);

                    <tr>
                        <td style="font-size: 17px; padding: 3px; text-align: right;">@invoiceDetail.HSNSACCode</td>
                        <td style="font-size: 17px; padding: 3px; text-align: right;">@sumNetTaxableAmount.IndianFormat()</td>

                        @if (isIntraState)
                        {
                            var sumNetCgstAmount = group.Sum(x => x.NetCGSTAmount ?? 0);
                            var sumNetSgstAmount = group.Sum(x => x.NetSGSTAmount ?? 0);

                            <td style="font-size: 17px; padding: 3px; text-align: center;">@(invoiceDetail.CGSTRate?.ToString("#,##0.##") ?? "")</td>
                            <td style="font-size: 17px; padding: 3px; text-align: right;">@sumNetCgstAmount.IndianFormat()</td>
                            <td style="font-size: 17px; padding: 3px; text-align: center;">@(invoiceDetail.SGSTRate?.ToString("#,##0.##") ?? "")</td>
                            <td style="font-size: 17px; padding: 3px; text-align: right;">@sumNetSgstAmount.IndianFormat()</td>
                        }
                        else
                        {
                            var sumNetIgstAmount = group.Sum(x => x.NetIGSTAmount ?? 0);

                            <td style="font-size: 17px; padding: 3px; text-align: center;">@(invoiceDetail.IGSTRate?.ToString("#,##0.##") ?? "")</td>
                            <td style="font-size: 17px; padding: 3px; text-align: right;">@sumNetIgstAmount.IndianFormat()</td>
                        }

                        <td class="total-value" style="font-size: 17px; padding: 3px; text-align: right;">@sumNetAmount.IndianFormat()</td>
                    </tr>
                }
            }

            </table>
            <!-- Right Table -->
            <div class="summary-right" style="padding-bottom: 4px; font-size: 17px !important; line-height: 1; padding-right: 3px;">
                @{
                    var invoiceDetails = Model.Invoice.InvoiceDetailsList;

                    <div class="entry single-line">
                        <span class="label">Total Unit Amount</span>
                        <span class="colon">:</span>
                        <span class="value">@invoiceDetails.Sum(d => d.NetUnitAmount ?? 0).IndianFormat()</span>
                    </div>

                    <div class="entry single-line">
                        <span class="label">Discount Amount</span>
                        <span class="colon">:</span>
                        <span class="value">@invoiceDetails.Sum(d => d.NetDiscountAmount ?? 0).IndianFormat()</span>
                    </div>

                    <div class="entry single-line">
                        <span class="label"><strong>Total Taxable Value</strong></span>
                        <span class="colon">:</span>
                        <span class="value"><strong>₹ @invoiceDetails.Sum(d => d.NetTaxableAmount ?? 0).IndianFormat()</strong></span>
                    </div>

                    @if (isIntraState)
                    {
                        <div class="entry">
                            <span class="label">CGST Amount</span>
                            <span class="colon">:</span>
                            <span class="value">@invoiceDetails.Sum(d => d.NetCGSTAmount ?? 0).IndianFormat()</span>
                        </div>
                        <div class="entry">
                            <span class="label">SGST Amount</span>
                            <span class="colon">:</span>
                            <span class="value">@invoiceDetails.Sum(d => d.NetSGSTAmount ?? 0).IndianFormat()</span>
                        </div>
                    }
                    else
                    {
                        <div class="entry">
                            <span class="label">IGST Amount</span>
                            <span class="colon">:</span>
                            <span class="value">@invoiceDetails.Sum(d => d.NetIGSTAmount ?? 0).IndianFormat()</span>
                        </div>
                    }

                    <div class="entry">
                        <span class="label">Round Off</span>
                        <span class="colon">:</span>
                        <span class="value">@Model.Invoice.RoundingOff</span>
                    </div>

                    <div class="entry" style="font-size: 19px !important;">
                        <span class="label"><strong>Total Value</strong></span>
                        <span class="colon">:</span>
                        <span class="value"><strong>₹ @(Model.Invoice.GrandTotal?.IndianFormat() ?? string.Empty)</strong></span>
                    </div>
                }
            </div>
        </div>

        <table class="footer-note" style="width: 100%; border: 0.25px solid black; border-collapse: collapse; margin-top: 0;">
            <tr>
                <td style="width: 7%; border-right: 0.25px solid black; text-align: left; vertical-align: top; padding: 5px; font-size: 17px;">E & OE</td>
                <td style="width: 93%; text-align: left; vertical-align: top; padding-right: 3px !important; padding: 5px; font-size: 17px;">Amount in words: @Model.InvoiceValueInWords</td>
            </tr>
        </table>

        <!-- Bank Details and Signature Section -->
        @{
            var hasQRCode = !string.IsNullOrEmpty(Model.ClientBankAccount.QRCode);
        }
        <table style="width: 100%; border-top: none; border: 0.25px solid black; border-collapse: collapse; margin-top: 0; height: auto; min-height: 160px; table-layout: auto;">
            <tr>
                <td style="width: 60%; text-align: left; vertical-align: top; font-size: 17px; height: 100%; overflow: auto;">
                    <div class="bank-details-container" style="padding: 3px;">
                        @if (hasQRCode)
                        {
                            <div class="bank-qr-code" style="display: inline-block; vertical-align: top; padding-right: 10px;">
                                <img src="/upload/@Model.ClientBankAccount.QRCode" onerror="this.style.display='none';" />
                            </div>
                        }
                        @if (Model.ClientBankAccount != null &&
                        (!string.IsNullOrWhiteSpace(Model.ClientBankAccount.BankName) ||
                        !string.IsNullOrWhiteSpace(Model.ClientBankAccount.BranchName) ||
                        !string.IsNullOrWhiteSpace(Model.ClientBankAccount.AccountNumber) ||
                        !string.IsNullOrWhiteSpace(Model.ClientBankAccount.IFSCCode)))
                        {
                            <div class="bank-details-text" style="display: inline-block; vertical-align: top; white-space: nowrap;">
                                <table style="margin-left: 0; margin-bottom: 0; border: none; text-align: left; width: 100%;">
                                    <tr>
                                        <td colspan="3" style="text-align: left; font-weight: bold; text-decoration: underline; padding-bottom: 5px;">
                                            Our Bank Details:
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: left; line-height: 1.2; width: 5%;">Bank</td>
                                        <td style="text-align: center; font-weight: bold; line-height: 1.2; width: 2%; padding-right: 5px;">:</td>
                                        <td style="text-align: left; line-height: 1.2;">@Model.ClientBankAccount.BankName</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: left; line-height: 1.2;">Branch</td>
                                        <td style="text-align: center; font-weight: bold; line-height: 1.2; padding-right: 5px;">:</td>
                                        <td style="text-align: left; line-height: 1.2;">@Model.ClientBankAccount.BranchName</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: left; line-height: 1.2;">Acc. No.</td>
                                        <td style="text-align: center; font-weight: bold; line-height: 1.2; padding-right: 5px;">:</td>
                                        <td style="text-align: left; line-height: 1.2;">@Model.ClientBankAccount.AccountNumber</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: left; line-height: 1.2;">IFSC</td>
                                        <td style="text-align: center; font-weight: bold; line-height: 1.2; padding-right: 5px;">:</td>
                                        <td style="text-align: left; line-height: 1.2;">@Model.ClientBankAccount.IFSCCode</td>
                                    </tr>
                                </table>
                            </div>
                        }
                    </div>

                    <!-- Remarks Section -->
                    <div style="width: 100%; text-align: left; padding-left: 3px; margin-top: 0; line-height: 1.1;">
                        @if (Model.Invoice.Remarks != null)
                        {
                            <p style="font-size: 17px; line-height: 1.2; margin: 0;"><strong>Disclaimer/Remarks:</strong> @Model.Invoice.Remarks</p>
                        }
                    </div>
                </td>
                <td style="width: 40%; text-align: center; vertical-align: middle; padding: 10px; border: none;">
                    <div style="display: inline-block; width: 100%; text-align: center; font-size: 17px;">
                        <strong>For @Model.Invoice.ClientName</strong>
                        <div id="signature-container" style="margin-top: 10px; margin-bottom: 10px; height: 80px;">
                            <img src='/upload/@Model.Client.ClientDSC' style='max-width: 300px; max-height: 80px; background-color: transparent; height: 80px; width: auto;' onerror="this.parentElement.style.height='50px'; this.style.display='none';" />
                        </div>
                        <span style="font-size: 17px;">Authorised Signatory</span>
                    </div>
                </td>
            </tr>
        </table>

        <div class="Powered-by" style="font-size: 12px; margin-top: 2px; word-wrap: break-word;">
            Powered by<strong> www.reconnbooks.com</strong>, hosted by <strong>Reconn Info Solutions India Pvt. Ltd.</strong>
        </div>

    </div>
</body>
</html>