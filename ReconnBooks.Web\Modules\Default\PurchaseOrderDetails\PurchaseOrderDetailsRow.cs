using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("PurchaseOrderDetails")]
[DisplayName("Purchase Order Details"), InstanceName("Purchase Order Details"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[LookupScript]
public sealed partial class PurchaseOrderDetailsRow : Row<PurchaseOrderDetailsRow.RowFields>, IIdRow, INameRow, IRowNumberedRow
{
    const string jPurchaseOrder = nameof(jPurchaseOrder);
    const string jCommodityType = nameof(jCommodityType);
    const string jCommodity = nameof(jCommodity);
    const string jHSNSACCode = nameof(jHSNSACCode);
    const string jUnit = nameof(jUnit);
    const string jGSTRate = nameof(jGSTRate);

//--Serial Numbering--

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

//--Purchase Order Details--------------------------

    [DisplayName("Purchase Order Detail Id"), Identity, IdProperty, NameProperty]
    public int? PurchaseOrderDetailId { get => fields.PurchaseOrderDetailId[this]; set => fields.PurchaseOrderDetailId[this] = value; }

    [DisplayName("Purchase Order"), NotNull, ForeignKey(typeof(PurchaseOrdersRow)), LeftJoin(jPurchaseOrder)]
    [TextualField(nameof(PurchaseOrderNo)), ServiceLookupEditor(typeof(PurchaseOrdersRow), Service = "Default/PurchaseOrders/List")]
    public int? PurchaseOrderId { get => fields.PurchaseOrderId[this]; set => fields.PurchaseOrderId[this] = value; }

    [DisplayName("Purchase Order No."), Origin(jPurchaseOrder, nameof(PurchaseOrdersRow.PurchaseOrderNo))]
    public string PurchaseOrderNo { get => fields.PurchaseOrderNo[this]; set => fields.PurchaseOrderNo[this] = value; }

//--Commodities--

    [DisplayName("Commodity Type"), NotNull, ForeignKey(typeof(CommodityTypesRow)), LeftJoin(jCommodityType)]
    [TextualField(nameof(CommodityType))]
    [ServiceLookupEditor(typeof(CommodityTypesRow), Service = "Default/CommodityTypes/List")]
    public int? CommodityTypeId { get => fields.CommodityTypeId[this]; set => fields.CommodityTypeId[this] = value; }

    [DisplayName("Commodity"), NotNull, ForeignKey(typeof(CommoditiesRow)), LeftJoin(jCommodity), TextualField(nameof(CommodityName))]
    [ServiceLookupEditor(typeof(CommoditiesRow), InplaceAdd = true, Service = "Default/Commodities/List", CascadeFrom = "CommodityTypeId", CascadeField = "CommodityTypeId")]
    [LookupInclude]
    public long? CommodityId { get => fields.CommodityId[this]; set => fields.CommodityId[this] = value; }

//--Fetching Product Code from Products Master--

    [DisplayName("Product Code")]
    [Origin(jCommodity, nameof(CommoditiesRow.CommodityCode)), LookupInclude]
    [CommodityCodeEditor(CascadeFrom = "CommodityTypeId", CascadeField = "CommodityTypeId")]
    public string CommodityCode { get => fields.CommodityCode[this]; set => fields.CommodityCode[this] = value; }

//--Commodity details--

    [DisplayName("Commodity Description"), QuickSearch]
    public string CommodityDescription { get => fields.CommodityDescription[this]; set => fields.CommodityDescription[this] = value; }

    [DisplayName("Commodity Type"), Origin(jCommodityType, nameof(CommodityTypesRow.CommodityType)), LookupInclude]
    public string CommodityType { get => fields.CommodityType[this]; set => fields.CommodityType[this] = value; }

    [DisplayName("Commodity Name"), Origin(jCommodity, nameof(CommoditiesRow.CommodityName)), LookupInclude]
    public string CommodityName { get => fields.CommodityName[this]; set => fields.CommodityName[this] = value; }

//--HSN/SAC Code details--

    [DisplayName("HSN/SAC Code"), Origin(jCommodity, nameof(CommoditiesRow.HSNSACCodeId))]
    [ForeignKey(typeof(HsnsacCodesRow)), LeftJoin(jHSNSACCode), TextualField(nameof(HSNSACCode))]
    [ServiceLookupEditor(typeof(HsnsacCodesRow), InplaceAdd = true, Service = "Default/HsnsacCodes/List")]
    public int? HSNSACCodeId { get => fields.HSNSACCodeId[this]; set => fields.HSNSACCodeId[this] = value; }

    [DisplayName("HSN/SAC Description")]
    [Origin(jHSNSACCode, nameof(HSNSACDescription)), LookupInclude]
    public string HSNSACDescription { get => fields.HSNSACDescription[this]; set => fields.HSNSACDescription[this] = value; }

    [DisplayName("HSN/SAC Group")]
    [Origin(jHSNSACCode, nameof(HSNSACGroup)), LookupInclude]
    public string HSNSACGroup { get => fields.HSNSACGroup[this]; set => fields.HSNSACGroup[this] = value; }

    [DisplayName("HSN/SAC Code")]
    [Origin(jHSNSACCode, nameof(HSNSACCode)), LookupInclude]
    public string HSNSACCode { get => fields.HSNSACCode[this]; set => fields.HSNSACCode[this] = value; }

//--Quantity details--

    [DisplayName("Quantity"), Size(18), Scale(2), NotNull]
    public decimal? Quantity { get => fields.Quantity[this]; set => fields.Quantity[this] = value; }

    //--Unit of Measure--

    [DisplayName("Unit of Measure"), NotNull, ForeignKey(typeof(UnitsRow)), LeftJoin(jUnit), TextualField(nameof(UnitName))]
    [ServiceLookupEditor(typeof(UnitsRow), InplaceAdd = true, Service = "Default/Units/List")]
    public int? UnitId { get => fields.UnitId[this]; set => fields.UnitId[this] = value; }

    [DisplayName("Unit Name"), Origin(jUnit, nameof(UnitsRow.UnitName)),LookupInclude]
    public string UnitName { get => fields.UnitName[this]; set => fields.UnitName[this] = value; }

//--Unit Price--

    [DisplayName("Unit Price"), Size(18), Scale(2), NotNull]
    public decimal? UnitPrice { get => fields.UnitPrice[this]; set => fields.UnitPrice[this] = value; }

    [DisplayName("Unit Amount"), Size(18), Scale(2), NotNull, NotMapped]
    public decimal? UnitAmount { get => fields.UnitAmount[this]; set => fields.UnitAmount[this] = value; }

//--PO Amendment Quantity details--

    [DisplayName("Amended Quantity"), Size(18), Scale(2)]
    public decimal? AmendedQuantity { get => fields.AmendedQuantity[this]; set => fields.AmendedQuantity[this] = value; }

    [DisplayName("Amended Unit Id")]
    public int? AmendedUnitId { get => fields.AmendedUnitId[this]; set => fields.AmendedUnitId[this] = value; }

    [DisplayName("Amended Price"), Size(18), Scale(2)]
    public decimal? AmendedPrice { get => fields.AmendedPrice[this]; set => fields.AmendedPrice[this] = value; }

    [DisplayName("Pending Quantity"), Size(18), Scale(2)]
    public decimal? PendingQuantity { get => fields.PendingQuantity[this]; set => fields.PendingQuantity[this] = value; }

//--Taxable Amount Details--

    [DisplayName("Taxable Amt./Unit"), Size(18), Scale(2), NotNull, NotMapped]
    public decimal? TaxableAmountPerUnit { get => fields.TaxableAmountPerUnit[this]; set => fields.TaxableAmountPerUnit[this] = value; }

    [DisplayName("Net Taxable Amt."), Size(18), Scale(2), NotNull, NotMapped]
    public decimal? NetTaxableAmount { get => fields.NetTaxableAmount[this]; set => fields.NetTaxableAmount[this] = value; }

    //--Discount Amount Details--

    [DisplayName("Discount %"), Size(18), Scale(2)]
    public decimal? DiscountPercent { get => fields.DiscountPercent[this]; set => fields.DiscountPercent[this] = value; }

    [DisplayName("Discount Amt./Unit"), Size(18), Scale(2)]
    public decimal? DiscountAmountPerUnit { get => fields.DiscountAmountPerUnit[this]; set => fields.DiscountAmountPerUnit[this] = value; }

    [DisplayName("Net Discount Amt."), Size(18), Scale(2)]
    public decimal? NetDiscountAmount { get => fields.NetDiscountAmount[this]; set => fields.NetDiscountAmount[this] = value; }


    //--GST Rate Details--

    [DisplayName("Gst Rate"), Column("GSTRateId"), NotNull, ForeignKey(typeof(GstRatesRow)), LeftJoin(jGSTRate)]
    [TextualField(nameof(GSTRateRemarks)), ServiceLookupEditor(typeof(GstRatesRow), Service = "Default/GstRates/List")]
    public int? GSTRateId { get => fields.GSTRateId[this]; set => fields.GSTRateId[this] = value; }

    [DisplayName("GST Rate"), Origin(jGSTRate, nameof(GstRatesRow.Remarks))]
    public string GSTRateRemarks { get => fields.GSTRateRemarks[this]; set => fields.GSTRateRemarks[this] = value; }

//--IGST Rate Details--

    [DisplayName("IGST Rate (%)"), Column("IGSTRate"), Size(18), Scale(2)]
    public decimal? IGSTRate { get => fields.IGSTRate[this]; set => fields.IGSTRate[this] = value; }

    [DisplayName("IGST Amt./Unit"), Column("IGSTAmountPerUnit"), Size(18), Scale(2), NotMapped]
    public decimal? IGSTAmountPerUnit { get => fields.IGSTAmountPerUnit[this]; set => fields.IGSTAmountPerUnit[this] = value; }

    [DisplayName("Net IGST Amt."), Column("NetIGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? NetIGSTAmount { get => fields.NetIGSTAmount[this]; set => fields.NetIGSTAmount[this] = value; }

//--CGST Rate Details--

    [DisplayName("CGST Rate"), Column("CGSTRate"), Size(18), Scale(2)]
    public decimal? CGSTRate { get => fields.CGSTRate[this]; set => fields.CGSTRate[this] = value; }

    [DisplayName("CGST Amt./Unit"), Column("CGSTAmountPerUnit"), Size(18), Scale(2), NotMapped]
    public decimal? CGSTAmountPerUnit { get => fields.CGSTAmountPerUnit[this]; set => fields.CGSTAmountPerUnit[this] = value; }

    [DisplayName("Net CGST Amt."), Column("NetCGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? NetCGSTAmount { get => fields.NetCGSTAmount[this]; set => fields.NetCGSTAmount[this] = value; }

//--SGST Rate Details--

    [DisplayName("SGST Rate"), Column("SGSTRate"), Size(18), Scale(2)]
    public decimal? SGSTRate { get => fields.SGSTRate[this]; set => fields.SGSTRate[this] = value; }

    [DisplayName("SGST Amt./Unit"), Column("SGSTAmountPerUnit"), Size(18), Scale(2), NotMapped]
    public decimal? SGSTAmountPerUnit { get => fields.SGSTAmountPerUnit[this]; set => fields.SGSTAmountPerUnit[this] = value; }

    [DisplayName("Net SGST Amt."), Column("NetSGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? NetSGSTAmount { get => fields.NetSGSTAmount[this]; set => fields.NetSGSTAmount[this] = value; }

//--Price Summary--

    [DisplayName("Dummy Field"), Size(200)]
    public string DummyField { get => fields.DummyField[this]; set => fields.DummyField[this] = value; }

    [DisplayName("Net Price/Unit"), Size(18), Scale(2), NotNull]
    public decimal? NetPricePerUnit { get => fields.NetPricePerUnit[this]; set => fields.NetPricePerUnit[this] = value; }

    [DisplayName("Net Amount"), Size(18), Scale(2), NotNull]
    public decimal? NetAmount { get => fields.NetAmount[this]; set => fields.NetAmount[this] = value; }

}