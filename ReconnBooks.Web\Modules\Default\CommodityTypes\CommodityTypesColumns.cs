using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.CommodityTypes")]
[BasedOnRow(typeof(CommodityTypesRow), CheckNames = true)]
public class CommodityTypesColumns
{
    public long RowNumber { get; set; }
    [EditLink, DisplayName("Db.Shared.RecordId"), Wid<PERSON>(50), Sort<PERSON><PERSON><PERSON>(1, descending: false), AlignCenter]
    public int CommodityTypeId { get; set; }
    [EditLink]
    public string CommodityType { get; set; }

    [Width(80), AlignCenter]
    public bool SetDefault { get; set; }
}