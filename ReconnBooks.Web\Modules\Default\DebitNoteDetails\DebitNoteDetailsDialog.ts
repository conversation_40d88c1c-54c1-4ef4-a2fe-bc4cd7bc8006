import { DebitNoteDetailsForm, DebitNoteDetailsRow, CommoditiesRow, CommodityTypesRow, CommoditiesService, GstRatesRow } from '@/ServerTypes/Default';
import { Decorators, getRemoteData, toId, EditorUtils, WidgetProps } from '@serenity-is/corelib';
import { PendingChangesConfirmGridEditorDialog } from '../../Common/Helpers/PendingChangesConfirmGridEditorDialog';

@Decorators.registerClass('ReconnBooks.Default.DebitNoteDetailsDialog')
export class DebitNoteDetailsDialog extends PendingChangesConfirmGridEditorDialog<DebitNoteDetailsRow> {
    protected getFormKey() { return DebitNoteDetailsForm.formKey; }
    protected getRowDefinition() { return DebitNoteDetailsRow; }
    //protected getService() { return DebitNoteDetailsService.baseUrl; }

    protected form = new DebitNoteDetailsForm(this.idPrefix);
    public IsSamePlaceOfSupply: boolean;
    protected commodity: CommoditiesRow;
    private previousCommodityValue: string;
    constructor(props: WidgetProps<any>) {
        super(props);

        this.form.CommodityTypeId.changeSelect2(() => {
            this.setCommodityLabelNames(this.form.CommodityTypeId.text);
        })

        this.form.CommodityCode.changeSelect2(async () => {
            var CommodityCode = toId(this.form.CommodityCode.value);
            if (CommodityCode != null) {
                var Commodity = (await CommoditiesRow.getLookupAsync()).itemById[CommodityCode];
                this.form.CommodityId.value = Commodity.CommodityId.toString();
                this.form.CommodityDescription.value = Commodity.CommodityDescription;
                this.form.CommodityCode.value = Commodity.CommodityId.toString();
            }
        })
    }

    private setCommodityLabelNames(commodityType: string) {
        if (commodityType === "Goods") {
            this.form.CommodityCode.element[0].parentElement.querySelector('label').textContent = 'Product Code';
            this.form.CommodityId.element[0].parentElement.querySelector('label').textContent = 'Product Name';
            this.form.CommodityDescription.element[0].parentElement.querySelector('label').textContent = 'Product Description';
        } else if (commodityType === "Service") {
            this.form.CommodityCode.element[0].parentElement.querySelector('label').textContent = 'Service Code';
            this.form.CommodityId.element[0].parentElement.querySelector('label').textContent = 'Service Name';
            this.form.CommodityDescription.element[0].parentElement.querySelector('label').textContent = 'Service Description';
        }
    }

    protected async afterLoadEntity() {
        await super.afterLoadEntity();

        this.previousCommodityValue = this.form.CommodityId.value;

        this.form.CommodityId.change(e => {
            var commodityId = toId(this.form.CommodityId.value);
            if (commodityId != null) {

                CommoditiesService.Retrieve({
                    EntityId: commodityId
                }, response => {
                    var commodity = response.Entity;
                    this.form.CommodityDescription.value = commodity.CommodityDescription;
                    this.form.CommodityCode.value = commodity.CommodityId.toString();
                });

                let currentCommodityValue = this.form.CommodityId.value;
                if (this.previousCommodityValue !== currentCommodityValue) {
                    this.form.PoQuantity.element.focus();
                    this.previousCommodityValue = currentCommodityValue;
                }
            }
        });
            
        if (this.IsSamePlaceOfSupply) {
            this.hideIGST();
            this.showCGST();
            this.showSGST();
        }
        else {
            this.showIGST();
            this.hideCGST();
            this.hideSGST();
        }

        if (this.isNew()) {
            this.form.GSTRateId.value = (await GstRatesRow.getLookupAsync()).items.find(item => item.Current)?.GSTRateId.toString();
            this.form.CommodityTypeId.value = (await CommodityTypesRow.getLookupAsync()).items.find(item => item.SetDefault)?.CommodityTypeId.toString();
        }
        else {
            var commodityType = ((await CommodityTypesRow.getLookupAsync()).itemById[this.form.CommodityTypeId.value]).CommodityType;
            this.form.CommodityCode.value = this.form.CommodityId.value;
            this.form.CommodityTypeId.set_readOnly(true);
            this.setCommodityLabelNames(commodityType);
            EditorUtils.setReadonly(this.form.CommodityTypeId.element.findFirst('.editor'), true);
        }
        this.setDialogsLoadedState();
    }

    //------------Showing and Hiding GST Rates---------
    showCGST() {
        this.element.findFirst(".CGSTRate").show();
        this.element.findFirst(".CGSTAmountPerUnit").show();
        this.element.findFirst(".NetCGSTAmount").show();
    }

    hideCGST() {
        this.element.findFirst(".CGSTRate").hide();
        this.element.findFirst(".CGSTAmountPerUnit").hide();
        this.element.findFirst(".NetCGSTAmount").hide();
    }

    showSGST() {
        this.element.findFirst(".SGSTRate").show();
        this.element.findFirst(".SGSTAmountPerUnit").show();
        this.element.findFirst(".NetSGSTAmount").show();
    }

    hideSGST() {
        this.element.findFirst(".SGSTRate").hide();
        this.element.findFirst(".SGSTAmountPerUnit").hide();
        this.element.findFirst(".NetSGSTAmount").hide();
    }

    showIGST() {
        this.element.findFirst(".IGSTRate").show();
        this.element.findFirst(".IGSTAmountPerUnit").show();
        this.element.findFirst(".NetIGSTAmount").show();
    }

    hideIGST() {

        this.element.findFirst(".IGSTRate").hide();
        this.element.findFirst(".IGSTAmountPerUnit").hide();
        this.element.findFirst(".NetIGSTAmount").hide();
    }

    protected updateTitle() {
        this.dialogTitle = "Edit Debit Note Details";
    }
}