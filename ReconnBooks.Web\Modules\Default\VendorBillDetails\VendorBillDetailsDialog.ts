import { VendorBillDetailsForm, VendorBillDetailsRow, CommoditiesRow, GstRatesRow, CommodityTypesRow, CommoditiesService } from '@/ServerTypes/Default';
import { Decorators, getRemoteData, toId, EditorUtils, WidgetProps } from '@serenity-is/corelib';
import { PendingChangesConfirmGridEditorDialog } from '../../Common/Helpers/PendingChangesConfirmGridEditorDialog';

@Decorators.registerClass('ReconnBooks.Default.VendorBillDetailsDialog')

export class VendorBillDetailsDialog extends PendingChangesConfirmGridEditorDialog<VendorBillDetailsRow> {
    protected getFormKey() { return VendorBillDetailsForm.formKey; }
    protected getRowDefinition() { return VendorBillDetailsRow; }
    //protected getService() { return VendorBillDetailsService.baseUrl; }

    protected form = new VendorBillDetailsForm(this.idPrefix);
    public IsSamePlaceOfSupply: boolean;
    protected commodity: CommoditiesRow;
    private previousCommodityValue: string;
    constructor(props: WidgetProps<any>) {
        super(props);

        this.form.BillQuantity.change(e => {
            this.calculateAmount();
            this.calculateDiscountAmount();
            this.CalculateNetTaxableAmount();
            this.CalculateTaxableAmountPerUnit();

            if (this.IsSamePlaceOfSupply) {
                this.calculateCGSTAmounts();
                this.calculateSGSTAmounts();
            }
            else {
                this.calculateIGSTAmounts();
            }

            this.calculateNetAmount();
        })

        this.form.DiscountPercent.change(e => {
            this.form.DiscountAmount.value = (this.form.UnitAmount.value * this.form.DiscountPercent.value) / 100;
            this.CalculateNetTaxableAmount();
            this.CalculateTaxableAmountPerUnit();

            if (this.IsSamePlaceOfSupply) {
                this.calculateCGSTAmounts();
                this.calculateSGSTAmounts();
            }
            else {
                this.calculateIGSTAmounts();
            }

            this.calculateNetAmount();
        });

        this.form.DiscountAmount.change(e => {
            this.form.DiscountPercent.value = (this.form.DiscountAmount.value / this.form.UnitAmount.value) * 100;
            this.CalculateNetTaxableAmount();
            this.CalculateTaxableAmountPerUnit();

            if (this.IsSamePlaceOfSupply) {
                this.calculateCGSTAmounts();
                this.calculateSGSTAmounts();
            }
            else {
                this.calculateIGSTAmounts();
            }

            this.calculateNetAmount();
        })

        this.form.UnitPrice.change(e => {
            this.calculateAmount();
            this.calculateDiscountAmount();
            this.CalculateNetTaxableAmount();
            this.CalculateTaxableAmountPerUnit();

            if (this.IsSamePlaceOfSupply) {
                this.calculateCGSTAmounts();
                this.calculateSGSTAmounts();
            }
            else {
                this.calculateIGSTAmounts();
            }

            this.calculateNetAmount();
        })

        this.form.CommodityTypeId.changeSelect2(() => {
            this.setCommodityLabelNames(this.form.CommodityTypeId.text);
        })

        this.form.CommodityCode.changeSelect2(async () => {
            var CommodityCode = toId(this.form.CommodityCode.value);
            if (CommodityCode != null) {
                var Commodity = (await CommoditiesRow.getLookupAsync()).itemById[CommodityCode];
                this.form.CommodityId.value = Commodity.CommodityId.toString();
                this.form.CommodityDescription.value = Commodity.CommodityDescription;
                this.form.CommodityCode.value = Commodity.CommodityId.toString();
                this.form.HSNSACCode.value = Commodity.HSNSACCode;
                this.form.HSNSACGroup.value = Commodity.HSNSACGroup;
                this.form.HSNSACDescription.value = Commodity.HSNSACDescription;
                this.form.BillQuantity.value = undefined;
                this.form.UnitId.value = Commodity.UnitId.toString();
                this.form.UnitPrice.value = Commodity.PurchasePrice;
                this.form.UnitAmount.value = undefined;
                this.form.DiscountPercent.value = undefined;
                this.form.DiscountAmount.value = undefined;
                this.form.NetTaxableAmount.value = undefined;
                if (Commodity.GSTRateId) {
                    this.form.GSTRateId.value = Commodity.GSTRateId.toString();
                    this.applyGST();
                }
            }
        })

        this.form.GSTRateId.changeSelect2(async () => {
            if (!this.form.GSTRateId.value) {
                this.clearGSTFields();
            }
            else {
                this.applyGST();
            }
        });
    }

    private clearGSTFields() {
        this.form.CGSTRate.value = undefined;
        this.form.SGSTRate.value = undefined;
        this.form.IGSTRate.value = undefined;
        this.form.NetCGSTAmount.value = undefined;
        this.form.NetSGSTAmount.value = undefined;
        this.form.NetIGSTAmount.value = undefined;
        this.form.CGSTAmountPerUnit.value = undefined;
        this.form.SGSTAmountPerUnit.value = undefined;
        this.form.IGSTAmountPerUnit.value = undefined;
        this.form.NetAmount.value = undefined;
        this.form.NetPricePerUnit.value = undefined;
    }

    private setCommodityLabelNames(commodityType: string) {
        if (commodityType === "Goods") {
            this.form.CommodityCode.element[0].parentElement.querySelector('label').textContent = 'Product Code';
            this.form.CommodityId.element[0].parentElement.querySelector('label').textContent = 'Product Name';
            this.form.CommodityDescription.element[0].parentElement.querySelector('label').textContent = 'Product Description';
        } else if (commodityType === "Service") {
            this.form.CommodityCode.element[0].parentElement.querySelector('label').textContent = 'Service Code';
            this.form.CommodityId.element[0].parentElement.querySelector('label').textContent = 'Service Name';
            this.form.CommodityDescription.element[0].parentElement.querySelector('label').textContent = 'Service Description';
        }
    }

    protected async afterLoadEntity() {
        await super.afterLoadEntity();

        this.previousCommodityValue = this.form.CommodityId.value;

        this.form.CommodityId.change(e => {
            var commodityId = toId(this.form.CommodityId.value);
            if (commodityId != null) {

                CommoditiesService.Retrieve({
                    EntityId: commodityId
                }, response => {
                    var commodity = response.Entity;
                    this.form.CommodityDescription.value = commodity.CommodityDescription;
                    this.form.CommodityCode.value = commodity.CommodityId.toString();
                    this.form.HSNSACCode.value = commodity.HSNSACCode;
                    this.form.HSNSACGroup.value = commodity.HSNSACGroup;
                    this.form.HSNSACDescription.value = commodity.HSNSACDescription;
                    this.form.BillQuantity.value = undefined;
                    this.form.UnitId.value = commodity.UnitId.toString();
                    this.form.UnitPrice.value = commodity.PurchasePrice;
                    this.form.UnitAmount.value = undefined;
                    this.form.DiscountPercent.value = undefined;
                    this.form.DiscountAmount.value = undefined;
                    this.form.NetTaxableAmount.value = undefined;
                    if (commodity.GSTRateId) {
                        this.form.GSTRateId.value = commodity.GSTRateId.toString();
                        this.applyGST();
                    }
                });

                let currentCommodityValue = this.form.CommodityId.value;
                if (this.previousCommodityValue !== currentCommodityValue) {
                    this.form.BillQuantity.element.focus();
                    this.previousCommodityValue = currentCommodityValue;
                }
            }
            else {
                // clear all fields
                this.clearFields();
            }
        });

        if (this.IsSamePlaceOfSupply) {
            this.hideIGST();
            this.showCGST();
            this.showSGST();
        }
        else {
            this.showIGST();
            this.hideCGST();
            this.hideSGST();
        }

        if (this.isNew()) {
            this.form.GSTRateId.value = (await GstRatesRow.getLookupAsync()).items.find(item => item.Current)?.GSTRateId.toString();
            this.form.CommodityTypeId.value = (await CommodityTypesRow.getLookupAsync()).items.find(item => item.SetDefault)?.CommodityTypeId.toString();
        }
        else {
            var commodityType = ((await CommodityTypesRow.getLookupAsync()).itemById[this.form.CommodityTypeId.value]).CommodityType;
            this.form.CommodityCode.value = this.form.CommodityId.value;
            this.form.CommodityTypeId.set_readOnly(true);
            this.setCommodityLabelNames(commodityType);
            EditorUtils.setReadonly(this.form.CommodityTypeId.element.findFirst('.editor'), true);
        }
        this.setDialogsLoadedState();
    }

    calculateNetAmount() {
        var taxableAmount = this.form.NetTaxableAmount.value;
        var cgstAmount = this.form.NetCGSTAmount.value;
        var sgstAmount = this.form.NetSGSTAmount.value;
        var igstAmount = this.form.NetIGSTAmount.value;

        this.form.NetAmount.value = taxableAmount + (this.IsSamePlaceOfSupply ? cgstAmount + sgstAmount : igstAmount);
        this.form.NetPricePerUnit.value = this.form.NetAmount.value / this.form.BillQuantity.value;

    }

    async applyGST() {
        var gstRateId = toId(this.form.GSTRateId.value);

        if (gstRateId != null) {
            var gstRate = (await GstRatesRow.getLookupAsync()).itemById[gstRateId];

            if (this.IsSamePlaceOfSupply) {
                this.form.CGSTRate.value = gstRate.CGSTPercent;
                this.form.SGSTRate.value = gstRate.SGSTPercent;
                this.calculateCGSTAmounts();
                this.calculateSGSTAmounts();
            }
            else {
                this.form.IGSTRate.value = gstRate.IGSTPercent;
                this.calculateIGSTAmounts();
            }
            this.calculateNetAmount();
        }
    }

    calculateIGSTAmounts() {
        var igstRate = this.form.IGSTRate.value;
        var taxableAmount = this.form.NetTaxableAmount.value;
        var quantity = this.form.BillQuantity.value;

        var netTaxableAmount = igstRate * taxableAmount / 100;
        this.form.NetIGSTAmount.value = netTaxableAmount;
        this.form.IGSTAmountPerUnit.value = netTaxableAmount / quantity;
    }

    calculateCGSTAmounts() {
        var cgstRate = this.form.CGSTRate.value;
        var taxableAmount = this.form.NetTaxableAmount.value;
        var quantity = this.form.BillQuantity.value;

        var netTaxableAmount = cgstRate * taxableAmount / 100;
        this.form.NetCGSTAmount.value = netTaxableAmount;
        this.form.CGSTAmountPerUnit.value = netTaxableAmount / quantity;
    }

    calculateSGSTAmounts() {
        var sgstRate = this.form.SGSTRate.value;
        var taxableAmount = this.form.NetTaxableAmount.value;
        var quantity = this.form.BillQuantity.value;

        var netTaxableAmount = sgstRate * taxableAmount / 100;
        this.form.NetSGSTAmount.value = netTaxableAmount;
        this.form.SGSTAmountPerUnit.value = netTaxableAmount / quantity;
    }

    calculateAmount() {
        var quantity = this.form.BillQuantity.value;
        var unitPrice = this.form.UnitPrice.value;

        this.form.UnitAmount.value = quantity * unitPrice;
    }

    CalculateNetTaxableAmount() {
        var amount = this.form.UnitAmount.value;
        var discountAmount = this.form.DiscountAmount.value;

        this.form.NetTaxableAmount.value = amount - discountAmount;
    }

    calculateDiscountAmount() {
        var amount = this.form.UnitAmount.value;
        var discountPercent = this.form.DiscountPercent.value;

        if (discountPercent && !isNaN(discountPercent)) {
            this.form.DiscountAmount.value = (amount * discountPercent) / 100;
        }
        else {
            //this.form.DiscountAmount.value = undefined;
        }
    }

    CalculateTaxableAmountPerUnit() {
        var quantity = this.form.BillQuantity.value;
        var taxableAmount = this.form.NetTaxableAmount.value;

        this.form.TaxableAmountPerUnit.value = taxableAmount / quantity;
    }

    showCGST() {
        this.element.findFirst(".CGSTRate").show();
        this.element.findFirst(".CGSTAmountPerUnit").show();
        this.element.findFirst(".NetCGSTAmount").show();
    }

    hideCGST() {
        this.element.findFirst(".CGSTRate").hide();
        this.element.findFirst(".CGSTAmountPerUnit").hide();
        this.element.findFirst(".NetCGSTAmount").hide();
    }

    showSGST() {
        this.element.findFirst(".SGSTRate").show();
        this.element.findFirst(".SGSTAmountPerUnit").show();
        this.element.findFirst(".NetSGSTAmount").show();
    }

    hideSGST() {
        this.element.findFirst(".SGSTRate").hide();
        this.element.findFirst(".SGSTAmountPerUnit").hide();
        this.element.findFirst(".NetSGSTAmount").hide();
    }

    showIGST() {
        this.element.findFirst(".IGSTRate").show();
        this.element.findFirst(".IGSTAmountPerUnit").show();
        this.element.findFirst(".NetIGSTAmount").show();
    }

    hideIGST() {
        this.element.findFirst(".IGSTRate").hide();
        this.element.findFirst(".IGSTAmountPerUnit").hide();
        this.element.findFirst(".NetIGSTAmount").hide();
    }

    protected updateTitle() {
        this.dialogTitle = "Edit Vendor Bill Details";
    }
    clearFields() {
        this.form.CommodityDescription.value = undefined;
        this.form.CommodityCode.value = undefined;
        this.form.HSNSACCode.value = undefined;
        this.form.HSNSACGroup.value = undefined;
        this.form.HSNSACDescription.value = undefined;
        this.form.BillQuantity.value = undefined;
        this.form.UnitId.value = undefined;
        this.form.UnitPrice.value = undefined;
        this.form.UnitAmount.value = undefined;
        this.form.GSTRateId.value = undefined;
        this.form.DiscountPercent.value = undefined;
        this.form.DiscountAmount.value = undefined;
        this.form.TaxableAmountPerUnit.value = undefined;
        this.form.NetTaxableAmount.value = undefined;
        this.form.CGSTRate.value = undefined;
        this.form.SGSTRate.value = undefined;
        this.form.IGSTRate.value = undefined;
        this.form.NetCGSTAmount.value = undefined;
        this.form.CGSTAmountPerUnit.value = undefined;
        this.form.NetSGSTAmount.value = undefined;
        this.form.SGSTAmountPerUnit.value = undefined;
        this.form.NetIGSTAmount.value = undefined;
        this.form.IGSTAmountPerUnit.value = undefined;
        this.form.NetAmount.value = undefined;
        this.form.NetPricePerUnit.value = undefined;
    }
}