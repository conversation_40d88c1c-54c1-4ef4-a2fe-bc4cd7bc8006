using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("Units")]
[DisplayName("Units"), InstanceName("Units"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[LookupScript]
[UniqueConstraint(new[] { "UnitName" })]

public sealed partial class UnitsRow : Row<UnitsRow.RowFields>, IIdRow, INameRow, IRowNumberedRow
{
    const string jUQC = nameof(jUQC);

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

    [DisplayName("Unit Id"), Identity, IdProperty]
    public int? UnitId { get => fields.UnitId[this]; set => fields.UnitId[this] = value; }

    [DisplayName("Unit of Measure"), Size(50), NotNull, QuickSearch, NameProperty]
    public string UnitName { get => fields.UnitName[this]; set => fields.UnitName[this] = value; }

    [DisplayName("Unit Description"), Size(50), NotNull, QuickSearch]
    public string UnitDescription { get => fields.UnitDescription[this]; set => fields.UnitDescription[this] = value; }

    [DisplayName("UQC"), Column("UQCId"), ForeignKey(typeof(UqCsRow)), LeftJoin(jUQC), TextualField(nameof(UQCQuantityName))]
    [ServiceLookupEditor(typeof(UqCsRow), InplaceAdd = true, Service = "Default/UqCs/List")]
    public int? UqcId { get => fields.UqcId[this]; set => fields.UqcId[this] = value; }

    [DisplayName("UQC Quantity Name"), Origin(jUQC, nameof(UqCsRow.QuantityName)), LookupInclude]
    public string UQCQuantityName { get => fields.UQCQuantityName[this]; set => fields.UQCQuantityName[this] = value; }

    [DisplayName("Is Default"), NotNull, LookupInclude]
    public bool? SetDefault { get => fields.SetDefault[this]; set => fields.SetDefault[this] = value; }
}