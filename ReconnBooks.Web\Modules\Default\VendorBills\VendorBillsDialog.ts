import { VendorBillsForm, VendorBillsRow, VendorBillsService, DocumentsRow, StatesRow, VendorsRow, TcsRatesRow, TdsRatesRow, SupplyTypesRow } from '@/ServerTypes/Default';
import { Decorators, formatDate, toId, getRemoteData, confirmDialog, WidgetProps, SaveResponse } from '@serenity-is/corelib';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { VerifyAuthorizeDialog } from '../../Common/Helpers/VerifyAuthorizeDialog';

@Decorators.registerClass('ReconnBooks.Default.VendorBillsDialog')
@Decorators.panel()
export class VendorBillsDialog extends VerifyAuthorizeDialog<VendorBillsRow> {
    protected getFormKey() { return VendorBillsForm.formKey; }
    protected getRowDefinition() { return VendorBillsRow; }
    protected getService() { return VendorBillsService.baseUrl; }

    protected form = new VendorBillsForm(this.idPrefix);
    private docType: string;
    private netTaxableAmount: number;

    constructor(props: WidgetProps<any>) {
        super(props);

        (this.form.VendorBillDetailsList.view as any).onRowsOrCountChanged.subscribe(async (e) => {
            e.stopPropagation();
            await this.calculateTaxesAndGrandTotal()
            this.form.VendorBillDetailsList.getGrid().focus();

            const grid = this.form.VendorBillDetailsList.getGrid();
            const rowCount = grid.getDataLength();
            if (rowCount > 0) {
                grid.scrollRowIntoView(rowCount - 1);
            }
        });

        (this.form.VendorBillDetailsList.view as any).onDataChanged.subscribe(async (e) => {
            e.preventDefault = false;
            await this.calculateTaxesAndGrandTotal()
        });

        this.form.TDSRate.changeSelect2(async a => {
            await this.calculateTDSAmount();
            this.calculateRoundedOffGrandTotal()
        })

        this.form.TCSRate.changeSelect2(async a => {
            await this.calculateTCSAmount();
            this.calculateRoundedOffGrandTotal()
        })

        //--Fetching Customer Billing Address--

        this.form.VendorId.change(a => {
            //setTimeout(async () => {
                var VendorId = toId(this.form.VendorId.value);
                if (VendorId != null) {
                    var Vendor = (await VendorsRow.getLookupAsync()).itemById[VendorId];
                    this.form.GSTIN.value = Vendor.GSTIN;
                    this.form.PlaceOfSupplyStateName.value = Vendor.PlaceOfSupplyStateName;

                    var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
                    this.form.VendorBillDetailsList.SetPlaceOfSupply(Vendor.PlaceOfSupplyId == userSupplyStateId ? true : false);
                }
                else {
                    this.clearVendorFields();
                }
            //}, 100);
        })

        this.form.PurchaseOrderId.changeSelect2(e => {
            if (this.form.PurchaseOrderId.value === '') {
                // Clear the details in the grid
                this.form.VendorBillDetailsList.value = [];
            }
            else {
                VendorBillsService.GetFromPurchaseOrderDetails({
                    EntityId: toId(this.form.PurchaseOrderId.value)
                },
                    response => {
                        this.form.VendorBillDetailsList.value = response.Entities;
                    });
            }
        });
    }

    private async calculateTDSAmount() {
        let tdsRate = toId(this.form.TDSRate.value);
        if (tdsRate) {
            let tdsRateRow = (await TdsRatesRow.getLookupAsync()).itemById[tdsRate];
            if (tdsRateRow) {
                this.form.TDSAmount.value = (tdsRateRow.TDSRate * this.netTaxableAmount) / 100;
            }
        }
    }

    private async calculateTCSAmount() {
        let tcsRate = toId(this.form.TCSRate.value);
        if (tcsRate) {
            let tcsRateRow = (await TcsRatesRow.getLookupAsync()).itemById[tcsRate];
            if (tcsRateRow) {
                this.form.TCSAmount.value = (tcsRateRow.TCSRate * this.netTaxableAmount) / 100;
            }
        }
    }

    //--Total Amount Calculation--
    private calculateRoundedOffGrandTotal(): void {

        //iterate thru each item in the grid and sum the NetAmount
        let totalNetAmount = this.form.VendorBillDetailsList.view.getItems().reduce((sum, item) => sum + (item.NetAmount || 0), 0);

        let preRoundTotal = totalNetAmount - (this.form.TDSAmount.value ?? 0) - (this.form.TCSAmount.value ?? 0);

        const roundOff = preRoundTotal - Math.floor(preRoundTotal);
        this.form.RoundingOff.value = parseFloat((roundOff >= 0.50 ? 1 - roundOff : -roundOff).toFixed(2));
        this.form.GrandTotal.value = preRoundTotal + this.form.RoundingOff.value;
    }

    private calculateNetTaxableAmount(): void {
        this.netTaxableAmount = this.form.VendorBillDetailsList.view.getItems().reduce((sum, item) => sum + (item.NetTaxableAmount || 0), 0);
    }

    private async calculateTaxesAndGrandTotal() {
        this.calculateNetTaxableAmount();
        await this.calculateTDSAmount();
        await this.calculateTCSAmount();
        this.calculateRoundedOffGrandTotal();
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        if (this.isNew()) {

            this.form.SupplyTypeId.value = (await SupplyTypesRow.getLookupAsync()).items.find(item => item.SetDefault)?.SupplyTypeId.toString();

            //Autonumbering
            if (!this.form.FinancialYearId?.value) {
                FinancialYearHelper.getCurrentFinancialYearId().then(currentFinancialYearId => {
                    this.form.FinancialYearId.value = currentFinancialYearId.toString();
                });
            }
            this.setDialogsLoadedState();
        }
        else {
            var placeOfSupplyStateId = (await StatesRow.getLookupAsync()).items.find(x => x.StateName == this.form.PlaceOfSupplyStateName.value)?.StateId;

            var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
            this.form.VendorBillDetailsList.SetPlaceOfSupply(placeOfSupplyStateId == userSupplyStateId ? true : false);

            this.setDialogsLoadedState();
        }
    }
    //--Cloning a Document (Save As)--

    protected updateInterface() {
        super.updateInterface();
        this.cloneButton.toggle(this.isEditMode());
    }

    protected getCloningEntity() {
        var clonedEntity = super.getCloningEntity();
        return clonedEntity;
    }
   
    protected save(callback: (response: SaveResponse) => void) {
        if (!this.form.VendorBillDetailsList.value || this.form.VendorBillDetailsList.value.length === 0) {
            confirmDialog("There are no items in the grid. Do you want to proceed with saving.",
                () => {
                    // If the user confirms, proceed with saving
                    super.save(callback);
                }
                // If the user cancels, do nothing
            );
        }
        else {
            super.save(callback);
        }
    }
    private clearVendorFields() {
        this.form.GSTIN.value = undefined;
        this.form.PlaceOfSupplyStateName.value = undefined;
        this.form.VendorBillDetailsList.value = undefined;
    }

}