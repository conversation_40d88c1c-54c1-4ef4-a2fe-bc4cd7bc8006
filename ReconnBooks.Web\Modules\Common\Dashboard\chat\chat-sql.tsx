/**
 * @jsxImportSource preact
 */
import { signal } from "@preact/signals";
import { useEffect } from "preact/hooks";
import { ChatSqlService, ChatSqlRequest } from "../../../ServerTypes/Common/ChatSqlService";

interface SqlChatMessage {
    id: string;
    type: 'user' | 'assistant' | 'error';
    content: string;
    timestamp: Date;
    sqlQuery?: string;
    queryResult?: any;
    metadata?: any;
}

const messages = signal<SqlChatMessage[]>([]);
const inputText = signal("");
const isLoading = signal(false);
const conversationId = signal<string>("");
const serviceStatus = signal<{ isAvailable: boolean; message: string } | null>(null);

export function ChatSql() {
    useEffect(() => {
        // Check service status on component mount
        checkServiceStatus();

        // Add welcome message
        if (messages.value.length === 0) {
            addMessage({
                id: generateId(),
                type: 'assistant',
                content: 'Hello! I can help you query your database using natural language. Just ask me questions like "Show me all customers" or "What are the top 5 products by sales?"',
                timestamp: new Date()
            });
        }
    }, []);

    const checkServiceStatus = async () => {
        try {
            const status = await ChatSqlService.GetStatus();
            serviceStatus.value = status;

            if (!status.isAvailable) {
                addMessage({
                    id: generateId(),
                    type: 'error',
                    content: `⚠️ ${status.message}. Please contact your administrator to configure the AI service.`,
                    timestamp: new Date()
                });
            }
        } catch (error) {
            serviceStatus.value = { isAvailable: false, message: 'Service unavailable' };
            addMessage({
                id: generateId(),
                type: 'error',
                content: '⚠️ Unable to connect to the SQL chat service. Please try again later.',
                timestamp: new Date()
            });
        }
    };

    const addMessage = (message: SqlChatMessage) => {
        messages.value = [...messages.value, message];

        // Auto-scroll to bottom
        setTimeout(() => {
            const chatContainer = document.querySelector('.sql-chat-messages');
            if (chatContainer) {
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
        }, 100);
    };

    const sendMessage = async () => {
        const userMessage = inputText.value.trim();
        if (!userMessage || isLoading.value) return;

        if (!serviceStatus.value?.isAvailable) {
            addMessage({
                id: generateId(),
                type: 'error',
                content: 'SQL chat service is not available. Please check the configuration.',
                timestamp: new Date()
            });
            return;
        }

        // Add user message
        addMessage({
            id: generateId(),
            type: 'user',
            content: userMessage,
            timestamp: new Date()
        });

        inputText.value = "";
        isLoading.value = true;

        try {
            const request: ChatSqlRequest = {
                message: userMessage,
                conversationId: conversationId.value || undefined
            };

            const response = await ChatSqlService.Query(request);

            if (!conversationId.value && response.conversationId) {
                conversationId.value = response.conversationId;
            }

            if (response.success) {
                let content = `Found ${response.metadata?.rowCount || 0} results.`;

                if (response.metadata?.wasQueryModified) {
                    content += ` (Query was modified for safety: ${response.metadata.modificationReason})`;
                }

                addMessage({
                    id: generateId(),
                    type: 'assistant',
                    content: content,
                    timestamp: new Date(),
                    sqlQuery: response.generatedSql,
                    queryResult: response.queryResult,
                    metadata: response.metadata
                });
            } else {
                addMessage({
                    id: generateId(),
                    type: 'error',
                    content: response.errorMessage || 'An error occurred while processing your request.',
                    timestamp: new Date(),
                    sqlQuery: response.generatedSql
                });
            }
        } catch (error) {
            addMessage({
                id: generateId(),
                type: 'error',
                content: 'Failed to process your request. Please try again.',
                timestamp: new Date()
            });
        } finally {
            isLoading.value = false;
        }
    };

    const handleKeyPress = (e: KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    };

    const generateId = () => {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    };

    const formatTimestamp = (timestamp: Date) => {
        return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

    const renderQueryResult = (result: any) => {
        if (!result || !result.data || !Array.isArray(result.data)) {
            return null;
        }

        const data = result.data.slice(0, 10); // Show max 10 rows
        if (data.length === 0) {
            return <div className="text-muted">No data returned</div>;
        }

        const columns = result.columnNames || Object.keys(data[0]);

        return (
            <div className="query-result mt-2">
                <div className="table-responsive">
                    <table className="table table-sm table-striped">
                        <thead>
                            <tr>
                                {columns.map((col: string) => (
                                    <th key={col} className="text-nowrap">{col}</th>
                                ))}
                            </tr>
                        </thead>
                        <tbody>
                            {data.map((row: any, index: number) => (
                                <tr key={index}>
                                    {columns.map((col: string) => (
                                        <td key={col} className="text-nowrap">
                                            {row[col] !== null && row[col] !== undefined ? String(row[col]) : ''}
                                        </td>
                                    ))}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                {result.hasMoreRows && (
                    <div className="text-muted small">
                        Showing first 10 rows of {result.rowCount} total results
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className="sql-chat-container">
            <style>{`
                .sql-chat-container .avatar {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 14px;
                    flex-shrink: 0;
                }

                .sql-chat-container .message-content {
                    background: #f8f9fa;
                    padding: 0.75rem;
                    border-radius: 0.5rem;
                    margin-bottom: 0.25rem;
                }

                .sql-chat-container .message.user .message-content {
                    background: #e3f2fd;
                }

                .sql-chat-container .message.error .message-content {
                    background: #ffebee;
                    border-left: 3px solid #f44336;
                }

                .sql-chat-container .sql-query pre {
                    font-size: 0.8rem;
                    margin: 0;
                    max-height: 150px;
                    overflow-y: auto;
                }

                .sql-chat-container .query-result {
                    max-height: 300px;
                    overflow: auto;
                    border: 1px solid #dee2e6;
                    border-radius: 0.25rem;
                }

                .sql-chat-container .typing-indicator {
                    display: flex;
                    align-items: center;
                    padding: 0.75rem;
                }

                .sql-chat-container .typing-indicator span {
                    height: 8px;
                    width: 8px;
                    background: #6c757d;
                    border-radius: 50%;
                    display: inline-block;
                    margin-right: 4px;
                    animation: typing 1.4s infinite ease-in-out;
                }

                .sql-chat-container .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
                .sql-chat-container .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

                @keyframes typing {
                    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
                    40% { transform: scale(1); opacity: 1; }
                }

                .sql-chat-container .table th,
                .sql-chat-container .table td {
                    padding: 0.25rem 0.5rem;
                    font-size: 0.875rem;
                    max-width: 200px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            `}</style>
            <div className="card">
                <div className="card-header d-flex justify-content-between align-items-center">
                    <h6 className="mb-0">
                        <i className="la la-database me-2"></i>
                        SQL Chat Assistant
                    </h6>
                    <div className="d-flex align-items-center">
                        <span className={`badge ${serviceStatus.value?.isAvailable ? 'bg-success' : 'bg-danger'} me-2`}>
                            {serviceStatus.value?.isAvailable ? 'Online' : 'Offline'}
                        </span>
                        <button
                            className="btn btn-sm btn-outline-secondary"
                            onClick={checkServiceStatus}
                            title="Refresh status"
                        >
                            <i className="la la-refresh"></i>
                        </button>
                    </div>
                </div>

                <div className="card-body p-0">
                    <div className="sql-chat-messages" style={{ height: '400px', overflowY: 'auto', padding: '1rem' }}>
                        {messages.value.map((message) => (
                            <div key={message.id} className={`message mb-3 ${message.type}`}>
                                <div className="d-flex align-items-start">
                                    <div className={`avatar me-2 ${message.type === 'user' ? 'bg-primary' : message.type === 'error' ? 'bg-danger' : 'bg-success'}`}>
                                        <i className={`la ${message.type === 'user' ? 'la-user' : message.type === 'error' ? 'la-exclamation-triangle' : 'la-robot'}`}></i>
                                    </div>
                                    <div className="flex-grow-1">
                                        <div className="message-content">
                                            <div className="message-text">{message.content}</div>
                                            {message.sqlQuery && (
                                                <div className="sql-query mt-2">
                                                    <small className="text-muted">Generated SQL:</small>
                                                    <pre className="bg-light p-2 rounded small">{message.sqlQuery}</pre>
                                                </div>
                                            )}
                                            {message.queryResult && renderQueryResult(message.queryResult)}
                                        </div>
                                        <small className="text-muted">{formatTimestamp(message.timestamp)}</small>
                                    </div>
                                </div>
                            </div>
                        ))}

                        {isLoading.value && (
                            <div className="message mb-3 assistant">
                                <div className="d-flex align-items-start">
                                    <div className="avatar me-2 bg-success">
                                        <i className="la la-robot"></i>
                                    </div>
                                    <div className="flex-grow-1">
                                        <div className="typing-indicator">
                                            <span></span>
                                            <span></span>
                                            <span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    <div className="card-footer">
                        <div className="input-group">
                            <textarea
                                className="form-control"
                                placeholder="Ask me about your data... (e.g., 'Show me all customers from last month')"
                                value={inputText.value}
                                onInput={(e) => inputText.value = (e.target as HTMLTextAreaElement).value}
                                onKeyPress={handleKeyPress}
                                rows={2}
                                disabled={isLoading.value || !serviceStatus.value?.isAvailable}
                            />
                            <button
                                className="btn btn-primary"
                                onClick={sendMessage}
                                disabled={isLoading.value || !inputText.value.trim() || !serviceStatus.value?.isAvailable}
                            >
                                {isLoading.value ? (
                                    <i className="la la-spinner la-spin"></i>
                                ) : (
                                    <i className="la la-paper-plane"></i>
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
