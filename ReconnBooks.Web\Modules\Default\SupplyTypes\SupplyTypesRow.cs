using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("SupplyTypes")]
[DisplayName("Supply Types"), InstanceName("Supply Types"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[LookupScript]
public sealed partial class SupplyTypesRow : Row<SupplyTypesRow.RowFields>, IIdRow, INameRow, IRowNumberedRow
{
    const string jNatureOfSupply = nameof(jNatureOfSupply);

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

    [DisplayName("Supply Type"), NotNull, QuickSearch, NameProperty]
    public string SupplyType { get => fields.SupplyType[this]; set => fields.SupplyType[this] = value; }

    [DisplayName("Nature Of Supply"), ForeignKey("NatureOfSupply", "NatureOfSupplyId"), LeftJoin(jNatureOfSupply)]
    [TextualField(nameof(NatureOfSupply))]
    public int? NatureOfSupplyId { get => fields.NatureOfSupplyId[this]; set => fields.NatureOfSupplyId[this] = value; }

    [DisplayName("Nature Of Supply"), Expression($"{jNatureOfSupply}.[NatureOfSupply]")]
    public string NatureOfSupply { get => fields.NatureOfSupply[this]; set => fields.NatureOfSupply[this] = value; }

    [DisplayName("Supply Type Id"), Identity, IdProperty]
    public int? SupplyTypeId { get => fields.SupplyTypeId[this]; set => fields.SupplyTypeId[this] = value; }

    [DisplayName("Is Default"), NotNull, LookupInclude]
    public bool? SetDefault { get => fields.SetDefault[this]; set => fields.SetDefault[this] = value; }
}