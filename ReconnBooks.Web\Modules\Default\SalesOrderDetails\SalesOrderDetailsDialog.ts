import { SalesOrderDetailsForm, SalesOrderDetailsRow, CommoditiesRow, GstRatesRow, CommodityTypesRow, CommoditiesService } from '@/ServerTypes/Default';
import { Decorators, getRemoteData, toId, EditorUtils } from '@serenity-is/corelib';
import { PendingChangesConfirmGridEditorDialog } from '../../Common/Helpers/PendingChangesConfirmGridEditorDialog';

@Decorators.registerClass('ReconnBooks.Default.SalesOrderDetailsDialog')
@Decorators.responsive()
export class SalesOrderDetailsDialog extends PendingChangesConfirmGridEditorDialog<SalesOrderDetailsRow> {
    protected getFormKey() { return SalesOrderDetailsForm.formKey; }
    protected getRowDefinition() { return SalesOrderDetailsRow; }
    //protected getService() { return SalesOrderDetailsService.baseUrl; }

    protected form = new SalesOrderDetailsForm(this.idPrefix);
    //public PlaceOfSupplyStateId: number;
    public IsSamePlaceOfSupply: boolean;
    protected commodity: CommoditiesRow;
    private previousCommodityValue: string;
    constructor() {
        super();

        this.form.OrderQuantity.change(e => {
            this.calculateAmount();
            this.calculateNetDiscountAmount();
            this.CalculateNetTaxableAmount();
            this.CalculateTaxableAmountPerUnit();
            this.CalculateDiscountAmountPerUnit();

            if (this.IsSamePlaceOfSupply) {
                this.calculateCGSTAmounts();
                this.calculateSGSTAmounts();
            }
            else {
                this.calculateIGSTAmounts();
            }
            this.calculateNetAmount();
        })

        this.form.DiscountPercent.change(e => {
            this.form.NetDiscountAmount.value = (this.form.OrderUnitAmount.value * this.form.DiscountPercent.value) / 100;
            this.CalculateNetTaxableAmount();
            this.CalculateTaxableAmountPerUnit();
            this.CalculateDiscountAmountPerUnit();

            if (this.IsSamePlaceOfSupply) {
                this.calculateCGSTAmounts();
                this.calculateSGSTAmounts();
            }
            else {
                this.calculateIGSTAmounts();
            }
            this.calculateNetAmount();
        });

        this.form.NetDiscountAmount.change(e => {
            this.form.DiscountPercent.value = (this.form.NetDiscountAmount.value / this.form.OrderUnitAmount.value) * 100;
            this.CalculateNetTaxableAmount();
            this.CalculateTaxableAmountPerUnit();
            this.CalculateDiscountAmountPerUnit();

            if (this.IsSamePlaceOfSupply) {
                this.calculateCGSTAmounts();
                this.calculateSGSTAmounts();
            }
            else {
                this.calculateIGSTAmounts();
            }
            this.calculateNetAmount();
        })

        this.form.OrderUnitPrice.change(e => {
            this.calculateAmount();
            this.calculateNetDiscountAmount();
            this.CalculateNetTaxableAmount();
            this.CalculateTaxableAmountPerUnit();
            this.CalculateDiscountAmountPerUnit();

            if (this.IsSamePlaceOfSupply) {
                this.calculateCGSTAmounts();
                this.calculateSGSTAmounts();
            }
            else {
                this.calculateIGSTAmounts();
            }
            this.calculateNetAmount();
        })

        this.form.CommodityTypeId.changeSelect2(() => {
            this.setCommodityLabelNames(this.form.CommodityTypeId.text);
        })

        //------------Fetching Details of CommodityCode---------
        this.form.CommodityCode.changeSelect2(async () => {
            var CommodityCode = toId(this.form.CommodityCode.value);
            if (CommodityCode != null) {
                var Commodity = (await CommoditiesRow.getLookupAsync()).itemById[CommodityCode];
                this.form.CommodityId.value = Commodity.CommodityId.toString();
                this.form.CommodityDescription.value = Commodity.CommodityDescription;
                this.form.CommodityCode.value = Commodity.CommodityId.toString();
                this.form.HSNSACCode.value = Commodity.HSNSACCode;
                this.form.HSNSACGroup.value = Commodity.HSNSACGroup;
                this.form.HSNSACDescription.value = Commodity.HSNSACDescription;
                this.form.OrderQuantity.value = undefined;
                this.form.OrderUnitId.value = Commodity.UnitId.toString();
                this.form.OrderUnitPrice.value = Commodity.SalesPrice;
                this.form.OrderUnitAmount.value = undefined;
                this.form.DiscountPercent.value = undefined;
                this.form.NetDiscountAmount.value = undefined;
                this.form.NetTaxableAmount.value = undefined;
                this.form.TaxableAmountPerUnit.value = undefined;
                if (Commodity.GSTRateId) {
                    this.form.GSTRateId.value = Commodity.GSTRateId.toString();
                    this.applyGST();
                }
            }
        })

        this.form.GSTRateId.changeSelect2(async () => {
            if (!this.form.GSTRateId.value) {
                this.clearGSTFields();
            }
            else {
                this.applyGST();
            }
        });
    }

    private clearGSTFields() {
        this.form.CGSTRate.value = undefined;
        this.form.SGSTRate.value = undefined;
        this.form.IGSTRate.value = undefined;
        this.form.NetCGSTAmount.value = undefined;
        this.form.NetSGSTAmount.value = undefined;
        this.form.NetIGSTAmount.value = undefined;
        this.form.CGSTAmountPerUnit.value = undefined;
        this.form.SGSTAmountPerUnit.value = undefined;
        this.form.IGSTAmountPerUnit.value = undefined;
        this.form.NetAmount.value = undefined;
        this.form.PricePerUnit.value = undefined;
    }

    //------------Setting Commodity Label Names---------
    private setCommodityLabelNames(commodityType: string) {
        if (commodityType === "Goods") {
            this.form.CommodityCode.element[0].parentElement.querySelector('label').textContent = 'Product Code';
            this.form.CommodityId.element[0].parentElement.querySelector('label').textContent = 'Product Name';
            this.form.CommodityDescription.element[0].parentElement.querySelector('label').textContent = 'Product Description';
        } else if (commodityType === "Service") {
            this.form.CommodityCode.element[0].parentElement.querySelector('label').textContent = 'Service Code';
            this.form.CommodityId.element[0].parentElement.querySelector('label').textContent = 'Service Name';
            this.form.CommodityDescription.element[0].parentElement.querySelector('label').textContent = 'Service Description';
        }
    }

    protected async afterLoadEntity() {
        await super.afterLoadEntity();

        this.previousCommodityValue = this.form.CommodityId.value;

        //------------Fetching Details of CommodityId---------
        this.form.CommodityId.change(e => {
            var CommodityId = toId(this.form.CommodityId.value);
            if (CommodityId != null) {

                CommoditiesService.Retrieve({
                    EntityId: CommodityId
                }, response => {
                    var commodity = response.Entity;
                    this.form.CommodityDescription.value = commodity.CommodityDescription;
                    this.form.CommodityCode.value = this.form.CommodityId.value;
                    this.form.HSNSACCode.value = commodity.HSNSACCode;
                    this.form.HSNSACGroup.value = commodity.HSNSACGroup;
                    this.form.HSNSACDescription.value = commodity.HSNSACDescription;
                    this.form.OrderQuantity.value = undefined;
                    this.form.OrderUnitId.value = commodity.UnitId.toString();
                    this.form.OrderUnitPrice.value = commodity.SalesPrice;
                    this.form.OrderUnitAmount.value = undefined;
                    this.form.DiscountPercent.value = undefined;
                    this.form.NetDiscountAmount.value = undefined;
                    this.form.NetTaxableAmount.value = undefined;
                    this.form.TaxableAmountPerUnit.value = undefined;
                    if (commodity.GSTRateId) {
                        this.form.GSTRateId.value = commodity.GSTRateId.toString();
                        this.applyGST();
                    }
                });

                let currentCommodityValue = this.form.CommodityId.value;
                if (this.previousCommodityValue !== currentCommodityValue) {
                    this.form.OfferQuantity.element.focus();
                    this.previousCommodityValue = currentCommodityValue;
                }
            }
            else {
                // clear all fields
                this.clearFields();
            }
        });

        if (this.IsSamePlaceOfSupply) {
            this.hideIGST();
            this.showCGST();
            this.showSGST();
        }
        else {
            this.showIGST();
            this.hideCGST();
            this.hideSGST();
        }

        if (this.isNew()) {
            this.form.GSTRateId.value = (await GstRatesRow.getLookupAsync()).items.find(item => item.Current)?.GSTRateId.toString();
            this.form.CommodityTypeId.value = (await CommodityTypesRow.getLookupAsync()).items.find(item => item.SetDefault)?.CommodityTypeId.toString();
        }
        else {
            var commodityType = ((await CommodityTypesRow.getLookupAsync()).itemById[this.form.CommodityTypeId.value]).CommodityType;
            this.form.CommodityCode.value = this.form.CommodityId.value;
            this.form.CommodityTypeId.set_readOnly(true);
            this.setCommodityLabelNames(commodityType);
            EditorUtils.setReadonly(this.form.CommodityTypeId.element.findFirst('.editor'), true);

            this.calculateAmount();
            this.CalculateDiscountAmountPerUnit();
            this.CalculateNetTaxableAmount();
            this.CalculateTaxableAmountPerUnit();
            if (this.IsSamePlaceOfSupply) {
                this.calculateCGSTAmounts();
                this.calculateSGSTAmounts();
            }
            else {
                this.calculateIGSTAmounts();
            }
            this.calculateNetAmount();
        }
       this.setDialogsLoadedState();
    }

    //------------Calculating Net Amount---------
    calculateNetAmount() {
        var taxableAmount = this.form.NetTaxableAmount.value;
        var cgstAmount = this.form.NetCGSTAmount.value;
        var sgstAmount = this.form.NetSGSTAmount.value;
        var igstAmount = this.form.NetIGSTAmount.value;

        this.form.NetAmount.value = taxableAmount + (this.IsSamePlaceOfSupply ? cgstAmount + sgstAmount : igstAmount);
        this.form.TaxableAmountPerUnit.value = this.form.NetTaxableAmount.value / this.form.OrderQuantity.value;
        this.form.PricePerUnit.value = this.form.NetAmount.value / this.form.OrderQuantity.value;
    }

    async applyGST() {
        var gstRateId = toId(this.form.GSTRateId.value);

        if (gstRateId != null) {
            var gstRate = (await GstRatesRow.getLookupAsync()).itemById[gstRateId];

            if (this.IsSamePlaceOfSupply) {
                this.form.CGSTRate.value = gstRate.CGSTPercent;
                this.form.SGSTRate.value = gstRate.SGSTPercent;
                this.calculateCGSTAmounts();
                this.calculateSGSTAmounts();
            }
            else {
                this.form.IGSTRate.value = gstRate.IGSTPercent;
                this.calculateIGSTAmounts();
            }
            this.calculateNetAmount();
        }
    }

    //------------Calculating IGST Amounts---------
    calculateIGSTAmounts() {
        var igstRate = this.form.IGSTRate.value;
        var basicAmount = this.form.NetTaxableAmount.value;
        var quantity = this.form.OrderQuantity.value;

        var netBasicAmount = igstRate * basicAmount / 100;
        this.form.NetIGSTAmount.value = netBasicAmount;
        this.form.IGSTAmountPerUnit.value = netBasicAmount / quantity;
    }

    //------------Calculating CGST Amounts---------
    calculateCGSTAmounts() {
        var cgstRate = this.form.CGSTRate.value;
        var basicAmount = this.form.NetTaxableAmount.value;
        var quantity = this.form.OrderQuantity.value;

        var netBasicAmount = cgstRate * basicAmount / 100;
        this.form.NetCGSTAmount.value = netBasicAmount;
        this.form.CGSTAmountPerUnit.value = netBasicAmount / quantity;
    }

    //------------Calculating SGST Amounts---------
    calculateSGSTAmounts() {
        var sgstRate = this.form.SGSTRate.value;
        var basicAmount = this.form.NetTaxableAmount.value;
        var quantity = this.form.OrderQuantity.value;

        var netBasicAmount = sgstRate * basicAmount / 100;
        this.form.NetSGSTAmount.value = netBasicAmount;
        this.form.SGSTAmountPerUnit.value = netBasicAmount / quantity;
    }

    //------------Calculating Amount---------
    calculateAmount() {
        var quantity = this.form.OrderQuantity.value;
        var unitPrice = this.form.OrderUnitPrice.value;

        this.form.OrderUnitAmount.value = quantity * unitPrice;
    }

    //------------Calculating Net Basic Amount---------
    CalculateNetTaxableAmount() {
        var amount = this.form.OrderUnitAmount.value;
        var discountAmount = this.form.NetDiscountAmount.value;

        this.form.NetTaxableAmount.value = amount - discountAmount;
    }

    //------------Calculating Net Discount Amount---------
    calculateNetDiscountAmount() {
        var amount = this.form.OrderUnitAmount.value;
        var discountPercent = this.form.DiscountPercent.value;

        if (discountPercent && !isNaN(discountPercent)) {
            this.form.NetDiscountAmount.value = (amount * discountPercent) / 100;
        }
        else {
            this.form.NetDiscountAmount.value = undefined;
        }
    }

    //------------Calculating Basic Amount Per Unit---------
    CalculateTaxableAmountPerUnit() {
        var quantity = this.form.OrderQuantity.value;
        var basicAmount = this.form.NetTaxableAmount.value;

        this.form.TaxableAmountPerUnit.value = basicAmount / quantity;
    }

    CalculateDiscountAmountPerUnit() {
        this.form.DiscountAmountPerUnit.value = this.form.NetDiscountAmount.value / this.form.OrderQuantity.value;
    }

    //------------Showing and Hiding GST Rates---------
    showCGST() {
        this.element.findFirst(".CGSTRate").show();
        this.element.findFirst(".CGSTAmountPerUnit").show();
        this.element.findFirst(".NetCGSTAmount").show();
    }

    hideCGST() {
        this.element.findFirst(".CGSTRate").hide();
        this.element.findFirst(".CGSTAmountPerUnit").hide();
        this.element.findFirst(".NetCGSTAmount").hide();
    }

    showSGST() {
        this.element.findFirst(".SgstRate").show();
        this.element.findFirst(".SgstAmountPerUnit").show();
        this.element.findFirst(".NetSgstAmount").show();
    }

    hideSGST() {
        this.element.findFirst(".SGSTtRate").hide();
        this.element.findFirst(".SGSTAmountPerUnit").hide();
        this.element.findFirst(".NetSGSTAmount").hide();
    }

    showIGST() {
        this.element.findFirst(".IGSTRate").show();
        this.element.findFirst(".IGSTAmountPerUnit").show();
        this.element.findFirst(".NetIGSTAmount").show();
    }

    hideIGST() {
        this.element.findFirst(".IGSTRate").hide();
        this.element.findFirst(".IGSTAmountPerUnit").hide();
        this.element.findFirst(".NetIGSTAmount").hide();
    }

    protected updateTitle() {
        this.dialogTitle = "Edit Sales Order Details";
    }
    clearFields() {
        this.form.CommodityDescription.value = undefined;
        this.form.CommodityCode.value = undefined;
        this.form.HSNSACCode.value = undefined;
        this.form.HSNSACDescription.value = undefined;
        this.form.HSNSACGroup.value = undefined;
        this.form.OrderQuantity.value = undefined;
        this.form.OfferQuantity.value = undefined;
        this.form.OfferUnitId.value = undefined;
        this.form.OfferPrice.value = undefined;
        this.form.OrderUnitAmount.value = undefined;
        this.form.OrderUnitPrice.value = undefined;
        this.form.DiscountPercent.value = undefined;
        this.form.NetDiscountAmount.value = undefined;
        this.form.DiscountAmountPerUnit.value = undefined;
        this.form.NetTaxableAmount.value = undefined;
        this.form.TaxableAmountPerUnit.value = undefined;
        this.form.GSTRateId.value = undefined;
        this.form.CGSTRate.value = undefined;
        this.form.SGSTRate.value = undefined;
        this.form.IGSTRate.value = undefined;
        this.form.NetCGSTAmount.value = undefined;
        this.form.CGSTAmountPerUnit.value = undefined;
        this.form.NetSGSTAmount.value = undefined;
        this.form.SGSTAmountPerUnit.value = undefined;
        this.form.NetIGSTAmount.value = undefined;
        this.form.IGSTAmountPerUnit.value = undefined;
        this.form.NetAmount.value = undefined;
        this.form.PricePerUnit.value = undefined;
    }
}