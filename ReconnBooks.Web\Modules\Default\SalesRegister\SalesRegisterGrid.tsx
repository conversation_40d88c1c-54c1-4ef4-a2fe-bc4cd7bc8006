import { Decorators, EntityGrid, WidgetProps, GridRowSelectionMixin, LookupEditor } from '@serenity-is/corelib';
import { SalesRegisterColumns, SalesRegisterRow, SalesRegisterService } from '../../ServerTypes/Default';
import { ExcelExportHelper, PdfExportHelper } from "@serenity-is/extensions";
import { HeaderFiltersMixin } from "@serenity-is/pro.extensions";
import { BulkReportAction } from '../../Common/Reporting/BulkReportAction';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';

@Decorators.registerClass('ReconnBooks.Default.SalesRegisterGrid')
export class SalesRegisterGrid extends EntityGrid<SalesRegisterRow> {
    protected getColumnsKey() { return SalesRegisterColumns.columnsKey; }
    protected getRowDefinition() { return SalesRegisterRow; }
    protected getService() { return SalesRegisterService.baseUrl; }

    private rowSelection: GridRowSelectionMixin;

    constructor(props: WidgetProps<any>) {
        super(props);

        new HeaderFiltersMixin({
            grid: this
        });
    }

    protected createToolbarExtensions() {
        super.createToolbarExtensions();
        this.rowSelection = new GridRowSelectionMixin(this);
    }

    protected async createQuickFilters(): Promise<void> {
        await super.createQuickFilters();
        const currentFinancialYearId = await FinancialYearHelper.getCurrentFinancialYearId();
        this.findQuickFilter(LookupEditor, "FinancialYearId").values = [currentFinancialYearId.toString()];
    }

    protected getButtons() {
        var buttons = super.getButtons();

        // Remove the "Add" button
        buttons = buttons.filter(x => x.cssClass !== "add-button");

        buttons.push(ExcelExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            service: SalesRegisterService.baseUrl + '/ListExcel',
            separator: true,
            hint: "",
            title: "Excel"
        }));

        buttons.push(PdfExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            title: "PDF"
        }));

        //buttons.push({
        //    title: 'Get Selected Sales Register Reports',
        //    separator: true,
        //    cssClass: 'send-button',
        //    icon: 'fas fa-receipt',
        //    onClick: () => {
        //        if (!this.onViewSubmit()) {
        //            return;
        //        }

        //        var action = new BulkReportAction({
        //            key: 'QuotationReport',
        //            pdfFileName: 'Quotation',
        //            zippedFileName: 'Quotations',
        //        });
        //        action.execute(this.rowSelection.getSelectedKeys());
        //        action.done = () => this.rowSelection.resetCheckedAndRefresh();
        //    }
        //});
        return buttons;
    }
}