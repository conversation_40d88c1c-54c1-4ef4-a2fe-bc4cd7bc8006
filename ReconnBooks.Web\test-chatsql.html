<!DOCTYPE html>
<html>
<head>
    <title>ChatSQL Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        textarea { width: 100%; height: 100px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>ChatSQL Service Test</h1>
        
        <div class="test-section">
            <h3>1. Service Status Test</h3>
            <button onclick="testStatus()">Check Service Status</button>
            <div id="statusResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. Database Schema Test</h3>
            <button onclick="testSchema()">Get Database Schema</button>
            <div id="schemaResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. Query Test (without AI)</h3>
            <p>This will test the query endpoint without AI processing:</p>
            <textarea id="queryInput" placeholder="Enter a test message...">Show me all customers</textarea>
            <br>
            <button onclick="testQuery()">Test Query</button>
            <div id="queryResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. Manual SQL Test</h3>
            <p>Test SQL execution directly (for debugging):</p>
            <textarea id="sqlInput" placeholder="Enter SQL query...">SELECT TOP 5 * FROM Customers</textarea>
            <br>
            <button onclick="testSql()">Execute SQL</button>
            <div id="sqlResult" class="result"></div>
        </div>
    </div>

    <script>
        async function makeRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                const result = await response.json();
                
                return {
                    success: response.ok,
                    data: result,
                    status: response.status
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }
        
        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            if (result.success) {
                element.className = 'result success';
                element.innerHTML = '<strong>Success:</strong><br><pre>' + JSON.stringify(result.data, null, 2) + '</pre>';
            } else {
                element.className = 'result error';
                element.innerHTML = '<strong>Error:</strong><br>' + (result.error || JSON.stringify(result.data, null, 2));
            }
        }
        
        async function testStatus() {
            const result = await makeRequest('/api/chat-sql/status');
            displayResult('statusResult', result);
        }
        
        async function testSchema() {
            const result = await makeRequest('/api/chat-sql/schema');
            displayResult('schemaResult', result);
        }
        
        async function testQuery() {
            const message = document.getElementById('queryInput').value;
            const data = {
                message: message,
                conversationId: null,
                includeSchema: false
            };
            
            const result = await makeRequest('/api/chat-sql/query', 'POST', data);
            displayResult('queryResult', result);
        }
        
        async function testSql() {
            // This would be a direct SQL execution test
            // For now, just show that the endpoint would be called
            const sql = document.getElementById('sqlInput').value;
            const element = document.getElementById('sqlResult');
            element.className = 'result';
            element.innerHTML = '<strong>Note:</strong> Direct SQL execution would require a separate test endpoint.<br>SQL: ' + sql;
        }
    </script>
</body>
</html>
