using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.Units")]
[BasedOnRow(typeof(UnitsRow), CheckNames = true)]
public class UnitsColumns
{
    public long RowNumber { get; set; }
    [EditLink, Width(120)]
    public string UnitName { get; set; }

    [Width(180)]
    public string UnitDescription { get; set; }

    [DisplayName("UQC"), Width(170)]
    public string UQCQuantityName { get; set; }

    [Width(80), AlignCenter]
    public bool SetDefault { get; set; }

    [DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: false), AlignCenter]
    public int UnitId { get; set; }
}