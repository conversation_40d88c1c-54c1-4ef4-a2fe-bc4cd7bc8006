@using ReconnBooks.Modules.Common.Helpers;
@model ReconnBooks.Modules.Default.PurchaseOrders.Reporting.PurchaseOrderReportData
@{
    Layout = "";
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purchase Orders</title>
    <!-- Include Bootstrap CSS from CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0px 0px 10px 15px; /* top right bottom left */
            padding: 0px;
            font-family: Calibri, sans-serif;
        }

        .header-table,
        .PurchaseOrders-table,
        .Vendor-table,
        .Product-table,
        .HeaderNote-table,
        .FooterNote-table {
            width: 100%; /* Ensures the table takes full width */
            border-collapse: collapse;
            margin-bottom: 0px;
        }
        
        .header-table td,
            .PurchaseOrders-table td,
            .Vendor-table td {
            vertical-align: middle;
        }

        .qr-code {
            width: 80px; /* Fixed width for both logo and QR code */
            height: auto; /* Maintain aspect ratio */
        }

        .logo {
            max-width: 100%; /* Take full width available */
            height: auto; /* Maintain aspect ratio */
        }

        .address-container {
            text-align: left;
            word-wrap: break-word; /* Ensure text wraps within column */
        }
        
        .address-container strong {  
            font-size: 25px;
            padding: 1px;
        }

        /* PurchaseOrders Table Styling */
        .PurchaseOrders-table td {
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            padding: 3px;
            background-color: #f0f0f0;
            border: 0.25px solid black;
        }

        .header-table .PurchaseOrders-table-cell {
            border: 0.25px solid black;
        }

        /* For header-like bold text */
        .Vendor-table td strong {
            font-weight: bold;
        }

        /* Vendor Info Styling */
        .vendor-info {
            vertical-align: top;
            width: 420px;
            padding-left: 3px; /* Add left padding for alignment */
        }

        /* Purchase Info and Date Styling */
        .purchase-info, .purchaseNo-info, .date-info {
            height: 30px;
            text-align: left;
            vertical-align: middle;
            line-height: 30px; /* Ensures vertical centering */
            padding-left: 3px; /* Add left padding for consistency */
        }

        /* Column width adjustments */
        .purchase-info {
            width: 110px !important;
            max-width: 110px;
            min-width: 110px;
            border-right: none !important;
            white-space: normal;
            word-wrap: break-word;
            overflow-wrap: break-word;
            line-height: 1.1;
        }


        .purchaseNo-info {
            width: 150px;
            max-width: 150px;
            min-width: 150px;
            border-right: none !important;
            white-space: normal;
            word-wrap: break-word;
            overflow-wrap: break-word;
            line-height: 1.1;
        }

        .date-info {
            width: 65px;
            max-width: 65px;
            min-width: 65px;
            border-left: none;
            white-space: normal;
            word-wrap: break-word;
            overflow-wrap: break-word;
            line-height: 1.1;
        }


        /* Vendor Table Styling */
        .Vendor-table {
            border-collapse: collapse;
            width: 100%;
        }
        
        .Vendor-table td {
            border: 0.25px solid black;
            padding: 2px 3px; /* Add left padding */
        }
        
        /* Ensure the right border of date-info cell remains */
        .Vendor-table td.date-info:last-child {
            border-left: none;
        }
        
        /* Fixed height for first three rows */
        .Vendor-table tr:not(:last-child) {
            height: 30px;
        }
        
        /* Last row should expand dynamically */
        .Vendor-table tr:last-child td {
            height: auto;
            padding: 10px; /* Maintain proper spacing */
        }

        .header-table {
            table-layout: fixed; /* Prevent columns from resizing */
        }

        .Product-table {
            width: 100%;
            table-layout: fixed; /* Ensures fixed column widths */
            border-collapse: collapse;
            margin-top: 0px;
            border: 0.25px solid black;
        }
        
        .Product-table td {
            text-align: left;
            border-left: 0.25px solid black; /* Vertical lines only */
            vertical-align: top; /* Align header content at the top */
            padding: 3px;
            line-height: 1.1 !important;
        }
        
        .Product-table th {
            padding: 2px;
            text-align: center !important;
            border-left: 0.25px solid black;
            font-size: 16px;
            height: 40px !important;
            line-height: 1.1 !important;
        }
        
        .Product-table th:first-child,
        .Product-table td:first-child {
            border-left: none; /* Remove the first vertical border */
        }
        
        .Product-table td {
            border-top: none; /* Remove horizontal lines */
            border-bottom: none; /* Remove horizontal lines */
        }

        .Product-table th {
            background-color: #f0f0f0;
        }

        .totals-row td {
            background-color: #f0f0f0;
            vertical-align: top; /* Align header content at the top */
        }
        
        .Product-table td, .Product-table th {
            word-wrap: break-word; /* Break long words */
            word-break: break-word; /* Break long words */
            white-space: normal; /* Allow wrapping */
        }
        
        .summary-table {
            width: 100%;
            margin-top: 0px;
            border: 0.25px solid black;
            border-collapse: collapse; /* Ensures a clean border appearance */
        }
        
        .summary-table td {
            padding: 3px;
            vertical-align: top;
            font-size: 15px;
        }

        .summary-left div {
            display: flex;
            align-items: baseline; /* Align text vertically */
            margin-bottom: 8px !important; /* Space between rows */
        }

        .summary-left .label {
            width: 110px; /* Adjust label width */
            text-align: left;
        }

        .summary-left .colon {
            width: 10px; /* Adjust width for colon */
            text-align: center;
        }

        .summary-left .value {
            margin-left: 10px; /* Space after colon */
            flex-grow: 1; /* Let value take remaining space */
        }

        .summary-right {
            width: 40px; /* Retains the width of the right section */
            text-align: right;
        }
        
        .summary-right .entry {
            display: flex;
            justify-content: flex-end; /* Aligns items to the right */
            margin-bottom: 8px; /* Adjusts space between entries */
        }
        
        .summary-right .label {
            display: inline-block;
            width: 150px;
            text-align: right; /* Aligns label to the right */
        }
        
        .summary-right .colon {
            display: inline-block;
            width: 20px; /* Adjusts the space between label and value */
            text-align: center; /* Keeps colon centered */
        }
        
        .summary-right .value {
            display: inline-block;
            width: 150px;
            text-align: right; /* Ensures value is aligned to the right */
            padding-left: 15px; /* Space between colon and value */
        }

        .summary-left .label,
        .summary-left .colon,
        .summary-left .value {
            font-size: 17px !important;
        }

        .summary-right .label,
        .summary-right .colon,
        .summary-right .value {
            font-size: 18px !important;
        }
        
        .bank-details {
            text-align: left;
            font-size: 16px;
        }
        
        .footer-note {
            margin-top: 0px;
            text-align: right;
            padding: 3px!important;
        }

        .header-table {
            width: 100vw;
            margin-left: -20px;
            margin-right: -20px;
            border-collapse: collapse;
        }
        
        .header-table td {
            padding: 2px !important;
            vertical-align: middle;
        }

        .dynamic-height-row {
            height: 50px; /* Default height that will be multiplied by the number of rows */
        }
        
        .dynamic-height-row td {
            border-left: 0.25px solid black; /* Match the border style of other cells */
            border-right: none;
            border-top: none;
            border-bottom: none;
        }
        
        .dynamic-height-row td:first-child {
            border-left: none;
        }
     
        .Product-table {
            /* Keep table layout as is without flexbox */
            table-layout: fixed; /* Ensure fixed column widths */
            width: 100%;
        }
        
        .Product-table tbody {
            display: table-row-group;
        }
   
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        .container-fluid {
            min-height: 100%;
            height: auto;
            width: 100%;
            box-sizing: border-box;
        }
    </style>
</head>
<body>

    <div class="container-fluid">
        <!-- Header Table -->
        <table class="header-table  ">
            <tr>
                <!-- Logo Column -->
                <td class="col-sm-2 text-center">
                    <img src="/upload/@Model.Client.Logo" alt="" class="logo" style="@(string.IsNullOrWhiteSpace(Model.Client.Logo) ? "display: none;" : "")" />
                </td>

                <!-- Address Column -->
                <td class="col-sm-9 address-container" style="line-height: 1.3 !important;">
                    <div style="font-size: 27px; text-align: center;"><strong>@Model.Client.ClientName</strong></div>
                    <div style="font-size: 17px; text-align: center;">
                        <div style="font-weight: bold;">@Model.Client.Address, @Model.Client.CityName - @Model.Client.PINCode<br /></div>

                        <div style="font-weight: bold;">
                            @(string.Join(" | ", new[] {
                            Model.Client.GSTIN != null ? $"GSTIN: {Model.Client.GSTIN}" : null,
                            Model.Client.PlaceOfSupplyStateCode != null ? $"{Model.Client.PlaceOfSupplyStateCode} - {Model.Client.PlaceOfSupplyStateCodeNo}" : null,
                            Model.Client.PAN != null ? $"PAN: {Model.Client.PAN}" : null,
                            Model.Client.UdyamNo != null ? $"UDYAM No.: {Model.Client.UdyamNo}" : null
                            }.Where(x => x != null)))<br />
                        </div>

                        @(string.Join(" | ", new[] {
                        Model.Client.HomePage,
                        Model.Client.EMail != null ? $"Email: {Model.Client.EMail}" : null,
                        Model.Client.PhoneNo != null ? $"Phone: {Model.Client.PhoneNo}" : (Model.Client.MobileNo != null ? $"Phone: {Model.Client.MobileNo}" : null)
                        }.Where(x => x != null)))<br />

                        @(string.Join(" | ", new[] {
                        Model.Client.CINNo != null ? $"CIN: {Model.Client.CINNo}" : null,
                        Model.Client.IECNo != null ? $"IECNo: {Model.Client.IECNo}" : null
                        }.Where(x => x != null)))
                    </div>

                    <div style="font-size: 17px!important; text-align: center; font-weight: bold;">@(Model.Client.TagLine != null ? Model.Client.TagLine : string.Empty)</div>
                </td>

                <!-- QR Code Column -->
                <td class="col-sm-2 text-center">
                    @{
                        if (!string.IsNullOrEmpty(Model.Client.Logo))

                        {

                            <img class="qr-code">
                        }
                    }
                </td>
            </tr>
        </table>

        <table class="PurchaseOrders-table">
            <tr>
                <td colspan="3"> PURCHASE ORDER </td>
            </tr>
        </table>

        <table class="Vendor-table" style="border: 0.25px solid black; border-collapse: collapse; width: 100%;">
            <tr>
                <!-- Vendor Info and Shipping Address Container -->
                <td class="Vendor-info-container" rowspan="7" style="vertical-align: top; width: 55%; border-right: 0.25px solid black;">
                    <div class="vendor-info" style="font-size: 18px; padding-left: 3px; margin: 0; width: 100%; box-sizing: border-box;">
                        <span>Vendor Name:</span><br>
                        <span><strong>@Model.Vendor.VendorName</strong></span><br>
                        <span>
                            @Model.Vendor.BillingAddress<br />
                            @Model.Vendor.BillingCityCityName - @Model.Vendor.BillingPinCode
                        </span><br>
                        @if (!string.IsNullOrWhiteSpace(Model.Vendor.PAN) || !string.IsNullOrWhiteSpace(Model.Vendor.UdyamNo))
                        {
                            <span style="display: block; margin-bottom: 10px;">
                                <strong>GST No.:@Model.Vendor.GSTIN</strong>,
                                @Model.Vendor.PlaceOfSupplyStateName 
                            </span>
                        }

                        @if (Model.PurchaseOrder.ShippingAddress != null || Model.PurchaseOrder.ShipToCustomerName != null || Model.PurchaseOrder.ShippingCityName != null || Model.PurchaseOrder.ShippingPinCode != null || Model.PurchaseOrder.ShippingGSTIN != null)
                        {
                            <span style="display: block; margin-top: 10px; text-decoration-line: underline;">Shipping Address:</span>
                            <span style="display: block;">
                                @Html.Raw(Model.PurchaseOrder.ShipToCustomerName != null ? $"<strong>{Model.PurchaseOrder.ShipToCustomerName}</strong><br />" : string.Empty)
                                @(Model.PurchaseOrder.ShippingAddress != null ? $"{Model.PurchaseOrder.ShippingAddress}" : string.Empty)<br />
                                @(Model.PurchaseOrder.ShippingCityName != null ? $"{Model.PurchaseOrder.ShippingCityName}" : string.Empty)
                                @(Model.PurchaseOrder.ShippingPinCode != null ? $" - {Model.PurchaseOrder.ShippingPinCode}" : string.Empty)
                                @if (Model.PurchaseOrder.ShippingCityName != null || Model.PurchaseOrder.ShippingPinCode != null)
                                {
                                    <br />
                                }
                                @(Model.PurchaseOrder.ShippingGSTIN != null ? $"GST No.: {Model.PurchaseOrder.ShippingGSTIN}" : string.Empty)
                                @(Model.PurchaseOrder.ShippingPlaceOfSupplyStateName != null ? $", {Model.PurchaseOrder.ShippingPlaceOfSupplyStateName}" : string.Empty)
                            </span>
                        }
                    </div>
       
                <td class="purchase-info" style="font-size: 17px;font-weight: bold;">
                    Purchase Order No.
                </td>
                <td class="purchaseNo-info" style="font-size: 17px;font-weight: bold;">
                    @Model.PurchaseOrder.PurchaseOrderNo
                </td>
                <td class="date-info" style="font-size: 17px;font-weight: bold;">
                    @Model.PurchaseOrder.PurchaseOrderDate.IndianFormatDate()
                </td>
            </tr>
            <tr>
                <td class="purchase-info" style="font-size: 17px;">
                    Reference No.
                </td>
                <td class="purchaseNo-info" style="font-size: 17px;">
                    @Model.PurchaseOrder.ReferenceNo
                </td>
                <td class="date-info" style="font-size: 17px;">
                    @Model.PurchaseOrder.ReferenceDate.IndianFormatDate()
                </td>
            </tr>
            <tr>
                <td colspan="3"></td>
            </tr>
        </table>

        <!-- HeaderNote Table -->
        @if (Model.PurchaseOrder.HeaderNote != null && Model.PurchaseOrder.HeaderNote.Trim() != "")
        {
            <table class=" HeaderNote-table" style="margin-top: 0px; font-size: 17px; text-align: left; margin-bottom: 0px; border: 0.25px solid black;min-height: 0px">
                <tr>
                    <td colspan="3" style="padding: 3px;">@Model.PurchaseOrder.HeaderNote</td>
                </tr>
            </table>
        }

        <table class="Product-table">
            <colgroup>
                <col style="width: @(Model.Vendor.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId ? "3px" : "3px");"> <!-- Sl. No. -->
                <col style="width: @(Model.Vendor.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId ? "27px" : "37px");"> <!-- Description -->
                <col style="width: @(Model.Vendor.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId ? "7px" : "8px");"> <!-- Qty Unit -->
                <col style="width: 10px;"> <!-- Unit Price -->
                <col style="width: 10px;"> <!-- Taxable Amt. -->
                @if (Model.Vendor.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId)

                {
                    <col style="width: 10px;"> <!-- CGST Amt. -->
                    <col style="width: 10px;"> <!-- SGST Amt. -->
                }

                else

                {
                    <col style="width: 10px;"> <!-- IGST Amt. -->
                }
                <col style="width: 10px;"> <!-- Net Price/Unit -->
                <col style="width: 12px;"> <!-- Net Amt. -->
            </colgroup>

            <thead>
                <tr>
                    <th></th>
                    <th style="border-left: none;">Product/Service Description</th>
                    <th>Qty.</th>
                    <th>Unit Price</th>
                    <th>Taxable Amt.</th>
                    @if (Model.Vendor.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId)

                    {
                        <th>CGST Amt.</th>
                        <th>SGST Amt.</th>
                    }

                    else

                    {
                        <th>IGST Amt.</th>
                    }
                    <th>Net Unit Price</th>
                    <th>Net Amt.</th>
                </tr>
            </thead>

            <tbody>
                @{
                    var counter = 1;

                    var isIntraState = Model.Vendor.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId;

                    var details = Model.PurchaseOrder.PurchaseOrderDetailsList;
                }

                @foreach (var purchaseOrderDetail in details)

                {
                    <tr>
                        <td style="font-size: 17px; text-align: center">@(counter++)</td>
                        <td style="width: 90%; text-align: left; font-size: 18px; border-left: none; line-height: 1.1;">
                            <strong>@purchaseOrderDetail.CommodityName</strong><br>
                            @if (!string.IsNullOrEmpty(purchaseOrderDetail.CommodityDescription))

                            {
                                <span style="font-size: 16px;">@purchaseOrderDetail.CommodityDescription</span>

                                <br>
                            }
                            @if (!string.IsNullOrEmpty(purchaseOrderDetail.CommodityCode))

                            {
                                <span style="font-size: 16px;">Part No.: @purchaseOrderDetail.CommodityCode</span>

                                <br>
                            }
                            @if (!string.IsNullOrEmpty(purchaseOrderDetail.HSNSACCode))

                            {
                                <span style="font-size: 16px;">HSN/SAC Code: @purchaseOrderDetail.HSNSACCode</span>
                            }
                        </td>
                        <td style="text-align: center; font-size: 17px">@purchaseOrderDetail.Quantity?.ToString("#,##0.##") @purchaseOrderDetail.UnitName</td>
                        <td style="text-align: right; font-size: 17px">@(purchaseOrderDetail.UnitPrice?.IndianFormat() ?? "")</td>
                        <td style="text-align: right; font-size: 17px">@(purchaseOrderDetail.NetTaxableAmount?.IndianFormat() ?? "")</td>

                        @if (isIntraState)

                        {
                            <td style="text-align: right; font-size: 17px">
                                @(purchaseOrderDetail.NetCGSTAmount?.IndianFormat() ?? "0")<br />
                                <span style="font-size: 15px;">@($"{purchaseOrderDetail.CGSTRate?.ToString("#0.##")}%" ?? "")</span>
                            </td>
                            <td style="text-align: right; font-size: 17px">
                                @(purchaseOrderDetail.NetSGSTAmount?.IndianFormat() ?? "0")<br />
                                <span style="font-size: 15px;">@($"{purchaseOrderDetail.SGSTRate?.ToString("#0.##")}%" ?? "")</span>
                            </td>
                        }

                        else

                        {
                            <td style="text-align: right; font-size: 17px">
                                @(purchaseOrderDetail.NetIGSTAmount?.IndianFormat() ?? "0")<br />
                                <span style="font-size: 15px;">@($"{purchaseOrderDetail.IGSTRate?.ToString("#0.##")}%" ?? "")</span>
                            </td>
                        }

                        <td style="text-align: right; font-size: 17px">@(purchaseOrderDetail.NetPricePerUnit?.IndianFormat() ?? "0")</td>
                        <td style="text-align: right; font-size: 17px"><strong>@(purchaseOrderDetail.NetAmount?.IndianFormat() ?? "")</strong></td>
                    </tr>
                }

                @{
                    var itemCount = details?.Count ?? 0;

                    var minRows = itemCount < 9 ? (9 - itemCount) : 0;
                }
                @for (int i = 0; i < minRows; i++)

                {
                    <tr class="dynamic-height-row">
                        <td></td>
                        <td style="border-left: none;"></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        @if (isIntraState)

                        {
                            <td></td>
                            <td></td>
                        }

                        else

                        {
                            <td></td>
                        }
                        <td></td>
                        <td></td>
                    </tr>
                }
            </tbody>

            <tfoot>
                @{
                    int columnCount = isIntraState ? 9 : 8;
                }
                <tr style="line-height: 1px; height: 1px;">
                    @for (int i = 0; i < columnCount; i++)

                    {

                        var style = "font-size: 17px;";

                        if (i == 0)
                            style += "border-right: none;";

                        else if (i == 1)
                            style += "border-left: none;";
                        <td style="@Html.Raw(style)"></td>
                    }
                </tr>
            </tfoot>
        </table>

        <table class="Product-table" style="margin-top: 0px!important; border-top: none!important;height: 35px !important;">
            <colgroup>
                <col style="width: @(Model.Vendor.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId ? "3px" : "3px");">  <!-- Sl. No. -->
                <col style="width: @(Model.Vendor.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId ? "27px" : "37px");"> <!-- Description -->
                <col style="width: @(Model.Vendor.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId ? "7px" : "8px");">  <!-- Qty Unit -->
                <col style="width: 10px;"> <!-- Unit Price -->
                <col style="width: 10px;"> <!-- Taxable Amt. -->
                @if (Model.Vendor.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId)
                {
                    <col style="width: 10px;"> <!-- CGST Amt. -->
                    <col style="width: 10px;"> <!-- SGST Amt. -->
                }
                else
                {
                    <col style="width: 10px;"> <!-- IGST Amt. -->
                }
                <col style="width: 10px;"> <!-- Net Price/Unit -->
                <col style="width: 12px;"> <!-- Net Amt. -->
            </colgroup>

            <tbody>
                <tr class="totals-row">
                    @{
                        if (Model.Vendor.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId)
                        {
                            <td style="font-size: 17px"></td>
                            <td style="font-size: 17px; text-align: right;border-left: none; vertical-align: middle">Total</td>
                            <td style="font-size: 17px"></td>
                            <td style="font-size: 17px"></td>
                            <td style="text-align: right; font-size: 17px; vertical-align: middle">@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetTaxableAmount ?? 0).IndianFormat()</td>
                            <td style="text-align: right; font-size: 17px; vertical-align: middle">@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetCGSTAmount ?? 0).IndianFormat()</td>
                            <td style="text-align: right; font-size: 17px; vertical-align: middle">@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetSGSTAmount ?? 0).IndianFormat()</td>
                            <td style="text-align: right; font-size: 17px; vertical-align: middle">@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetPricePerUnit ?? 0).IndianFormat()</td>
                            <td style="text-align: right; font-size: 17px; vertical-align: middle"><strong>@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetAmount ?? 0).IndianFormat()</strong></td>
                        }
                        else
                        {
                            <td style="font-size: 17px"></td>
                            <td style="font-size: 17px; text-align:right;border-left: none; vertical-align: middle">Total</td>
                            <td style="font-size: 17px"></td>
                            <td style="font-size: 17px"></td>
                            <td style="text-align: right; font-size: 17px; vertical-align: middle">@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetTaxableAmount ?? 0).IndianFormat()</td>
                            <td style="text-align: right; font-size: 17px; vertical-align: middle">@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetIGSTAmount ?? 0).IndianFormat()</td>
                            <td style="text-align: right; font-size: 17px; vertical-align: middle">@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetPricePerUnit ?? 0).IndianFormat()</td>
                            <td style="text-align: right; font-size: 17px; vertical-align: middle"><strong>@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetAmount ?? 0).IndianFormat()</strong></td>
                        }
                    }
                </tr>
                </tbody>
            </table>

        <table class="summary-table">
            <tr>
                <!-- Terms & Conditions -->
                <td class="summary-left">
                    <strong>Terms & Conditions:</strong><br>
                    <div><span class="label">Payment terms</span><span class="colon">:</span><span class="value">@Model.PurchaseOrder.PaymentTerms</span></div>
                    <div><span class="label">Taxes</span><span class="colon">:</span><span class="value">@Model.PurchaseOrder.Taxes</span></div>
                    @if (Model.PurchaseOrder.Inspection != null)

                    {
                        <div><span class="label">Inspection</span><span class="colon">:</span><span class="value">@Model.PurchaseOrder.Inspection</span></div>
                    }
                    @if (Model.PurchaseOrder.Remarks != null)

                    {
                        <div><span class="label">Note</span><span class="colon">:</span><span class="value">@Model.PurchaseOrder.Remarks</span></div>
                    }
                </td>

                <!-- Totals -->
                @{
                    if (Model.Vendor.PlaceOfSupplyId == Model.Client.PlaceOfSupplyId)

                    {
                        <td class="summary-right">
                            <div class="entry">
                                <span class="label" style="line-height: 1;">Total Unit Amount</span>
                                <span class="colon" style="line-height: 1;">:</span>
                                <span class="value" style="line-height: 1;">@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.UnitAmount ?? 0).IndianFormat()</span>
                            </div>
                            <div class="entry">
                                <span class="label" style="line-height: 1;">Discount Amount</span>
                                <span class="colon" style="line-height: 1;">:</span>
                                <span class="value" style="line-height: 1;">@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetDiscountAmount ?? 0).IndianFormat()</span>
                            </div>
                            <div class="entry">
                                <span class="label"><strong>Total Taxable Value</strong></span>
                                <span class="colon">:</span>
                                <span class="value"><strong>₹ @Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetTaxableAmount ?? 0).IndianFormat()</strong></span>
                            </div>
                            <div class="entry">
                                <span class="label">CGST Amount</span>
                                <span class="colon">:</span>
                                <span class="value">@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetCGSTAmount ?? 0).IndianFormat()</span>
                            </div>
                            <div class="entry">
                                <span class="label">SGST Amount</span>
                                <span class="colon">:</span>
                                <span class="value">@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetSGSTAmount ?? 0).IndianFormat()</span>
                            </div>
                            <div class="entry">
                                <span class="label">Round Off</span>
                                <span class="colon">:</span>
                                <span class="value">@Model.PurchaseOrder.RoundingOff</span>
                            </div>
                            <div class="entry">
                                <span class="label"><strong>Total Value</strong></span>
                                <span class="colon">:</span>
                                <span class="value"><strong>₹ @(Model.PurchaseOrder.GrandTotal?.IndianFormat() ?? "")</strong></span>
                            </div>
                        </td>
                    }

                    else

                    {
                        <td class="summary-right">
                            <div class="entry">
                                <span class="label" style="line-height: 1;">Total Unit Amount</span>
                                <span class="colon" style="line-height: 1;">:</span>
                                <span class="value" style="line-height: 1;">@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.UnitAmount ?? 0).IndianFormat()</span>
                            </div>
                            <div class="entry">
                                <span class="label" style="line-height: 1;">Discount Amount</span>
                                <span class="colon" style="line-height: 1;">:</span>
                                <span class="value" style="line-height: 1;">@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetDiscountAmount ?? 0).IndianFormat()</span>
                            </div>
                            <div class="entry">
                                <span class="label"><strong>Total Taxable Value</strong></span>
                                <span class="colon">:</span>
                                <span class="value"><strong>₹ @Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetTaxableAmount ?? 0).IndianFormat()</strong></span>
                            </div>
                            <div class="entry">
                                <span class="label">IGST Amount</span>
                                <span class="colon">:</span>
                                <span class="value">@Model.PurchaseOrder.PurchaseOrderDetailsList.Sum(d => d.NetIGSTAmount ?? 0).IndianFormat()</span>
                            </div>
                            <div class="entry">
                                <span class="label">Round Off</span>
                                <span class="colon">:</span>
                                <span class="value">@Model.PurchaseOrder.RoundingOff</span>
                            </div>
                            <div class="entry">
                                <span class="label"><strong>Total Value</strong></span>
                                <span class="colon">:</span>
                                <span class="value"><strong>₹ @(Model.PurchaseOrder.GrandTotal?.IndianFormat() ?? "")</strong></span>
                            </div>
                        </td>
                    }
                }
            </tr>
        </table>

        <div class="footer-note " style="border: 0.25px solid black;border-top: none; font-size: 17px; display: flex; justify-content: flex-start; text-align: left; padding-left : 3px;">
            Amount in words: @Model.PurchaseOrderValueInWords
        </div>

        <!-- FooterNote Table -->
        @if (Model.PurchaseOrder.FootNote != null && Model.PurchaseOrder.FootNote.Trim() != "")
        {
            <table class="FooterNote-table" style="margin-top: 0px; font-size: 17px; text-align: left; margin-bottom: 0px; border: 0.25px solid black;border-bottom: none;">
                <tr>
                    <td colspan="3" style="padding-left: 3px;">@Model.PurchaseOrder.FootNote</td>
                </tr>
            </table>
        }
        <!-- Remarks Section -->
        <table style="width: 100%;  border: 0.25px solid black; border-top: none; border-collapse: collapse; margin-top: 0px;">
            <tr>
                <!-- Remarks Section -->
                <td style="width: 60%; text-align: left; vertical-align: top; padding: 3px; border: none; font-size: 17px;">
                    <strong></strong><br>
                </td>

                <!-- Authorised Signatory Section -->
                <td style="width: 40%; text-align: center; vertical-align: middle; padding: 10px; border: none;">
                    <div style="display: inline-block; width: 100%; text-align: center; font-size: 17px;">
                        <strong> For @Model.PurchaseOrder.ClientName</strong>

                        <!-- Digital Signature (Fixed Space for Image) -->
                        <div id="signature-container" style="margin-top: 10px; margin-bottom: 10px; height: 80px;">
                            <img src='/upload/@Model.Client.ClientDSC'
                                 style='max-width: 300px; max-height: 80px; background-color: transparent; height: 80px; width: auto;'
                                 onerror="this.parentElement.style.height='50px'; this.style.display='none';" />
                        </div>

                        <span style="font-size: 16px;">Authorised Signatory</span>
                    </div>
                </td>
            </tr>
        </table>
        <div class="Powered-by" style="font-size: 12px; margin-top: 2px; word-wrap: break-word;">
            Powered by<strong> www.reconnbooks.com</strong>, hosted by <strong>Reconn Info Solutions India Pvt. Ltd.</strong>
        </div>
    </div>
</body>
</html> 