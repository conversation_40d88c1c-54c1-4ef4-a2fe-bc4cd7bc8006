using Serenity.ComponentModel;

namespace ReconnBooks.Default.Forms;

[FormScript("Default.InvoiceDetails")]
[BasedOnRow(typeof(InvoiceDetailsRow), CheckNames = true)]
public class InvoiceDetailsForm
{
    //public int InvoiceId { get; set; }

    [Category("Commodity Details"),Collapsible]
    [HalfWidth(UntilNext =true)]
    [DisplayName("Commodity Type")]
    public int CommodityTypeId { get; set; }

//--Products and Services--

    [DisplayName("Product Code")]
    public string CommodityCode { get; set; }

    [FullWidth]
    [DisplayName("Product Name")]
    public long CommodityId { get; set; }

    [TextAreaEditor(Rows = 3)]
    [DisplayName("Product Description")]
    public string CommodityDescription { get; set; }

    [DisplayName("Product Serial Nos")]
    public string ProductSerialNos { get; set; }

    [DisplayName("SKU")]
    public string SKU { get; set; }

    //--HSN/SAC Code--

    [Category("HSN/SAC Details"), Collapsible(Collapsed =true)]
    [HalfWidth(UntilNext =true)]
    [ReadOnly(true)]
    [DisplayName("HSN/SAC Code")]
    public string HSNSACCode { get; set; }

    [ReadOnly(true)]
    [DisplayName("HSN/SAC Group")]
    public string HSNSACGroup { get; set; }

    [FullWidth(UntilNext =true)]
    [ReadOnly(true)]
    [TextAreaEditor(Rows = 3)]
    [DisplayName("Description")]
    public string HSNSACDescription { get; set; }

//--Qty Details--

    [Category("Quantity & Price")]
    [OneThirdWidth(UntilNext = true)]
    [DisplayName("Quantity"), LabelWidth(110)]
    public decimal Quantity { get; set; }

    [DisplayName("Unit"), LabelWidth(50)]
    public int UnitId { get; set; }

    [EditLink]
    [DisplayName("Unit Price"), LabelWidth(80)]
    public decimal UnitPrice { get; set; }

//--Discount Details--

    [DisplayName("Discount %"), LabelWidth(110)]
    public decimal DiscountPercent { get; set; }

    [Hidden]
    [DisplayName("Discount/Unit"), LabelWidth(120)]
    public decimal DiscountAmountPerUnit { get; set; }

    [DisplayName("Discount Amt."), LabelWidth(125)]
    public decimal NetDiscountAmount { get; set; }

    [ReadOnly(true)]
    [DisplayName("Unit Amount"), LabelWidth(120)]
    public decimal NetUnitAmount { get; set; }

//--GST Details--
    [Category(" ")]
    [OneThirdWidth(UntilNext = true)]
    [DisplayName("GST Rate (%)"), LabelWidth(110)]
    public int GSTRateId { get; set; }

    [ReadOnly(true)]
    [DisplayName("Taxable Amt./Unit"), LabelWidth(125)]
    public decimal TaxableAmountPerUnit { get; set; }

//--Taxable Amount--
    [ReadOnly(true)]
    [DisplayName("Net Taxable Amt."), LabelWidth(120)]
    public decimal NetTaxableAmount { get; set; }

//--GST Details--

    [Category("  ")]
    [ReadOnly(true)]
    [DisplayName("IGST Rate (%)"), LabelWidth(110)]
    public decimal IGSTRate { get; set; }

    [ReadOnly(true)]
    [DisplayName("IGST Amt./Unit"), LabelWidth(125)]
    public decimal IGSTAmountPerUnit { get; set; }

    [ReadOnly(true)]
    [DisplayName("Net IGST Amt."), LabelWidth(120)]
    public decimal NetIGSTAmount { get; set; }

    [ReadOnly(true)]
    [DisplayName("CGST Rate (%)"), LabelWidth(110)]
    public decimal CGSTRate { get; set; }

    [ReadOnly(true)]
    [DisplayName("CGST Amt./Unit"), LabelWidth(125)]
    public decimal CGSTAmountPerUnit { get; set; }

    [ReadOnly(true)]
    [DisplayName("Net CGST Amt."), LabelWidth(120)]
    public decimal NetCGSTAmount { get; set; }

    [ReadOnly(true)]
    [DisplayName("SGST Rate (%)"), LabelWidth(110)]
    public decimal SGSTRate { get; set; }

    [ReadOnly(true)]
    [DisplayName("SGST Amt./Unit"), LabelWidth(125)]
    public decimal SGSTAmountPerUnit { get; set; }

    [ReadOnly(true)]
    [DisplayName("Net SGST Amt."), LabelWidth(120)]
    public decimal NetSGSTAmount { get; set; }

    [Category("   ")]
    [ReadOnly(true)]
    [DisplayName(), LabelWidth(0)] //Dummy field created to add blank Space on the form
    public decimal DummyField { get; set; }

    [ReadOnly(true)]
    [DisplayName("Net Price/Unit"), LabelWidth(125)]
    public decimal NetPricePerUnit { get; set; }
  
    [ReadOnly(true)]
    [DisplayName("Net Amount"), LabelWidth(120)]
    public decimal NetAmount { get; set; }
}


