﻿import { proxyTexts } from "@serenity-is/corelib";

namespace texts {

    export declare namespace Db {

        namespace Administration {

            namespace Language {
                export const Id: string;
                export const LanguageId: string;
                export const LanguageName: string;
            }

            namespace Role {
                export const ClientId: string;
                export const RoleId: string;
                export const RoleKey: string;
                export const RoleKeyOrName: string;
                export const RoleName: string;
            }

            namespace RolePermission {
                export const PermissionKey: string;
                export const RoleId: string;
                export const RoleKeyOrName: string;
                export const RolePermissionId: string;
            }

            namespace User {
                export const ClientCode: string;
                export const ClientId: string;
                export const ClientList: string;
                export const ClientLogo: string;
                export const ClientName: string;
                export const ConsultantId: string;
                export const ConsultantLogo: string;
                export const ConsultantName: string;
                export const DisplayName: string;
                export const Email: string;
                export const EmployeeId: string;
                export const ImpersonationToken: string;
                export const InsertDate: string;
                export const InsertUserId: string;
                export const IsActive: string;
                export const LastDirectoryUpdate: string;
                export const MobilePhoneNumber: string;
                export const Password: string;
                export const PasswordConfirm: string;
                export const PlaceOfSupplyId: string;
                export const Roles: string;
                export const RowNumber: string;
                export const Source: string;
                export const UpdateDate: string;
                export const UpdateUserId: string;
                export const UserId: string;
                export const UserImage: string;
                export const UserTypeDescription: string;
                export const UserTypeId: string;
                export const UserTypeName: string;
                export const Username: string;
            }

            namespace UserPermission {
                export const Granted: string;
                export const PermissionKey: string;
                export const User: string;
                export const UserId: string;
                export const UserPermissionId: string;
                export const Username: string;
            }

            namespace UserRole {
                export const RoleId: string;
                export const RoleKeyOrName: string;
                export const User: string;
                export const UserId: string;
                export const UserRoleId: string;
                export const Username: string;
            }
        }

        namespace Default {

            namespace AddressedTo {
                export const AddressedTo: string;
                export const AddressedToId: string;
                export const RowNumber: string;
            }

            namespace Banks {
                export const BankId: string;
                export const BankName: string;
                export const BankShortName: string;
                export const RowNumber: string;
            }

            namespace BusinessCategories {
                export const BusinessCategory: string;
                export const BusinessCategoryId: string;
                export const Description: string;
                export const RowNumber: string;
            }

            namespace BusinessGroups {
                export const BusinessGroup: string;
                export const BusinessGroupId: string;
                export const Description: string;
                export const RowNumber: string;
            }

            namespace BusinessTypes {
                export const BusinessType: string;
                export const BusinessTypeId: string;
                export const Description: string;
                export const RowNumber: string;
            }

            namespace Cities {
                export const CityId: string;
                export const CityName: string;
                export const District: string;
                export const DistrictId: string;
                export const PINCode: string;
                export const RowNumber: string;
                export const StateId: string;
                export const StateName: string;
            }

            namespace ClientBankAccounts {
                export const AccountName: string;
                export const AccountNumber: string;
                export const BankId: string;
                export const BankName: string;
                export const BranchCode: string;
                export const BranchName: string;
                export const ClientBankAccountId: string;
                export const ClientId: string;
                export const IFSCCode: string;
                export const QRCode: string;
                export const Status: string;
                export const SwiftCode: string;
            }

            namespace ClientUsers {
                export const ClientId: string;
                export const ClientLogo: string;
                export const ClientName: string;
                export const ClientUserId: string;
                export const ConsultantId: string;
                export const ConsultantName: string;
                export const Status: string;
                export const UserId: string;
                export const Username: string;
            }

            namespace Clients {
                export const Address: string;
                export const Address2: string;
                export const AlternateNo: string;
                export const BusinessCategory: string;
                export const BusinessCategoryId: string;
                export const BusinessGroup: string;
                export const BusinessGroupId: string;
                export const BusinessType: string;
                export const BusinessTypeId: string;
                export const CINNo: string;
                export const CityId: string;
                export const CityName: string;
                export const ClientBankAccountsList: string;
                export const ClientCode: string;
                export const ClientContactName: string;
                export const ClientDSC: string;
                export const ClientId: string;
                export const ClientName: string;
                export const ConsultantId: string;
                export const ConsultantName: string;
                export const Designation: string;
                export const DesignationId: string;
                export const Disclaimer: string;
                export const EMail: string;
                export const EmailServerHost: string;
                export const EmailServerPassword: string;
                export const EmailServerPasswordEncrypted: string;
                export const EmailServerPort: string;
                export const EmailServerUsername: string;
                export const FaxNo: string;
                export const GSTIN: string;
                export const HomePage: string;
                export const IECNo: string;
                export const InvoiceNoFormat: string;
                export const Logo: string;
                export const MobileNo: string;
                export const NatureOfSupply: string;
                export const NatureOfSupplyId: string;
                export const PAN: string;
                export const PINCode: string;
                export const PhoneNo: string;
                export const PlaceOfSupplyId: string;
                export const PlaceOfSupplyStateCode: string;
                export const PlaceOfSupplyStateCodeNo: string;
                export const PlaceOfSupplyStateName: string;
                export const RowNumber: string;
                export const SupplyType: string;
                export const SupplyTypeId: string;
                export const TANNo: string;
                export const TagLine: string;
                export const TitleId: string;
                export const TitleOfRespect: string;
                export const UdyamNo: string;
            }

            namespace Commodities {
                export const ClientId: string;
                export const CommodityCode: string;
                export const CommodityDescription: string;
                export const CommodityId: string;
                export const CommodityName: string;
                export const CommodityStatus: string;
                export const CommodityType: string;
                export const CommodityTypeId: string;
                export const GSTRateId: string;
                export const GstRateRemarks: string;
                export const HSNSACCode: string;
                export const HSNSACCodeId: string;
                export const HSNSACDescription: string;
                export const HSNSACGroup: string;
                export const ListPrice: string;
                export const MRP: string;
                export const ProductCategoryCategoryName: string;
                export const ProductCategoryId: string;
                export const ProductGroup: string;
                export const ProductGroupId: string;
                export const ProductImage: string;
                export const ProductMake: string;
                export const ProductMakeId: string;
                export const ProductType: string;
                export const ProductTypeId: string;
                export const ProductWeight: string;
                export const PurchasePrice: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const SalesPrice: string;
                export const UQCQuantityName: string;
                export const UnitId: string;
                export const UnitName: string;
            }

            namespace CommodityTypes {
                export const CommodityType: string;
                export const CommodityTypeId: string;
                export const RowNumber: string;
                export const SetDefault: string;
            }

            namespace ConsultantBankAccounts {
                export const AccountName: string;
                export const AccountNumber: string;
                export const BankId: string;
                export const BankName: string;
                export const BranchCode: string;
                export const BranchName: string;
                export const ConsultantBankAccountId: string;
                export const ConsultantId: string;
                export const IFSCCode: string;
                export const QRCode: string;
                export const Status: string;
                export const SwiftCode: string;
            }

            namespace Consultants {
                export const Address: string;
                export const AlternateNo: string;
                export const BusinessCategory: string;
                export const BusinessCategoryId: string;
                export const BusinessGroup: string;
                export const BusinessGroupId: string;
                export const BusinessType: string;
                export const BusinessTypeId: string;
                export const CINNo: string;
                export const CityId: string;
                export const CityName: string;
                export const ConsultantBankAccountsList: string;
                export const ConsultantCode: string;
                export const ConsultantDSC: string;
                export const ConsultantId: string;
                export const ConsultantName: string;
                export const ContactPerson: string;
                export const Designation: string;
                export const DesignationId: string;
                export const Disclaimer: string;
                export const EMail: string;
                export const FaxNo: string;
                export const GSTIN: string;
                export const HomePage: string;
                export const IECNo: string;
                export const InvoiceNoFormat: string;
                export const Logo: string;
                export const MobileNo: string;
                export const NatureOfSupply: string;
                export const NatureOfSupplyId: string;
                export const PAN: string;
                export const PINCode: string;
                export const PhoneNo: string;
                export const PlaceOfSupplyId: string;
                export const PlaceOfSupplyStateName: string;
                export const RowNumber: string;
                export const SupplyType: string;
                export const SupplyTypeId: string;
                export const TANNo: string;
                export const TagLine: string;
                export const TitleId: string;
                export const TitleOfRespect: string;
                export const UdyamNo: string;
            }

            namespace Countries {
                export const CountryId: string;
                export const CountryName: string;
                export const RowNumber: string;
            }

            namespace CreditNoteDetails {
                export const AssessmentRemarks: string;
                export const CGSTAmountPerUnit: string;
                export const CGSTRate: string;
                export const CommodityCode: string;
                export const CommodityDescription: string;
                export const CommodityId: string;
                export const CommodityName: string;
                export const CommodityType: string;
                export const CommodityTypeId: string;
                export const CreditNoteDetailId: string;
                export const CreditNoteId: string;
                export const CreditNoteNo: string;
                export const DiscountAmountPerUnit: string;
                export const DiscountPercent: string;
                export const DummyField: string;
                export const GSTRateId: string;
                export const GSTRateRemarks: string;
                export const IGSTAmountPerUnit: string;
                export const IGSTRate: string;
                export const InvoiceDetailCommodityDescription: string;
                export const InvoiceDetailId: string;
                export const InvoiceQuantity: string;
                export const InvoiceUnitId: string;
                export const InvoiceUnitUnitName: string;
                export const NetAmount: string;
                export const NetCGSTAmount: string;
                export const NetDiscountAmount: string;
                export const NetIGSTAmount: string;
                export const NetPricePerUnit: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const RejectionReason: string;
                export const RejectionReasonId: string;
                export const Remarks: string;
                export const ReplacementMethod: string;
                export const ReplacementMethodId: string;
                export const ReturnedQuantity: string;
                export const ReturnedUnitId: string;
                export const ReturnedUnitUnitName: string;
                export const RowNumber: string;
                export const SGSTAmountPerUnit: string;
                export const SGSTRate: string;
                export const SalesReturnDetailCommodityDescription: string;
                export const SalesReturnDetailId: string;
                export const SerialNos: string;
                export const TaxableAmountPerUnit: string;
                export const UnitAmount: string;
                export const UnitPrice: string;
            }

            namespace CreditNotes {
                export const AuthorizedByUserId: string;
                export const AuthorizedByUserUsername: string;
                export const AuthorizedDate: string;
                export const AuthorizedStatus: string;
                export const CancelledByUserId: string;
                export const CancelledByUserUsername: string;
                export const CancelledDate: string;
                export const ClientId: string;
                export const ClientName: string;
                export const CreditNoteAmount: string;
                export const CreditNoteDate: string;
                export const CreditNoteDetailsList: string;
                export const CreditNoteId: string;
                export const CreditNoteMonth: string;
                export const CreditNoteNo: string;
                export const CustomerCompanyName: string;
                export const CustomerId: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const InvoiceId: string;
                export const InvoiceNo: string;
                export const ModifiedByUserId: string;
                export const ModifiedByUserUsername: string;
                export const ModifiedDate: string;
                export const NetCGSTAmount: string;
                export const NetIGSTAmount: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const PlaceOfSupplyStateName: string;
                export const PreparedByUserId: string;
                export const PreparedByUserUsername: string;
                export const PreparedDate: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const SalesReturnId: string;
                export const SalesReturnNo: string;
                export const VerifiedByUserId: string;
                export const VerifiedByUserUsername: string;
                export const VerifiedDate: string;
            }

            namespace CustomerContacts {
                export const AlternateNo: string;
                export const ContactName: string;
                export const CustomerCompanyName: string;
                export const CustomerContactId: string;
                export const CustomerId: string;
                export const DepartmentId: string;
                export const DepartmentName: string;
                export const Designation: string;
                export const DesignationId: string;
                export const EMail: string;
                export const ExtensionNo: string;
                export const MobileNo: string;
                export const OfficePhoneNo: string;
                export const RowNumber: string;
                export const Status: string;
                export const TitleId: string;
                export const TitleOfRespect: string;
            }

            namespace CustomerDetails {
                export const CustomerDetailId: string;
                export const Email: string;
                export const LastContactDate: string;
                export const LastContactedByEmployeeFirstName: string;
                export const LastContactedByEmployeeId: string;
                export const SendBulletin: string;
            }

            namespace CustomerRepresentatives {
                export const CustomerId: string;
                export const EmployeeId: string;
                export const RepresentativeId: string;
            }

            namespace Customers {
                export const AccountName: string;
                export const AccountNumber: string;
                export const AddressedTo: string;
                export const AddressedToId: string;
                export const BankId: string;
                export const BankName: string;
                export const BillingAddress: string;
                export const BillingCityCityName: string;
                export const BillingCityId: string;
                export const BillingPinCode: string;
                export const BranchCode: string;
                export const BranchName: string;
                export const ClientId: string;
                export const ClientName: string;
                export const CompanyCode: string;
                export const CompanyName: string;
                export const CustomerContactsList: string;
                export const CustomerId: string;
                export const EMailId: string;
                export const FaxNo: string;
                export const GSTIN: string;
                export const HomePage: string;
                export const IECNo: string;
                export const IFSCCode: string;
                export const MailingAddress: string;
                export const MailingCityCityName: string;
                export const MailingCityId: string;
                export const MailingPINCode: string;
                export const MobileNo: string;
                export const NatureOfSupply: string;
                export const NatureOfSupplyId: string;
                export const PAN: string;
                export const PhoneNo: string;
                export const PlaceOfSupplyId: string;
                export const PlaceOfSupplyStateCode: string;
                export const PlaceOfSupplyStateCodeNo: string;
                export const PlaceOfSupplyStateName: string;
                export const RowNumber: string;
                export const SupplyType: string;
                export const SupplyTypeId: string;
                export const UdyamNo: string;
            }

            namespace DataAuditLog {
                export const ClientId: string;
                export const ClientName: string;
                export const FieldName: string;
                export const LogDate: string;
                export const LogId: string;
                export const LogType: string;
                export const NewValue: string;
                export const OldValue: string;
                export const RecordId: string;
                export const Tablename: string;
                export const UserDisplayName: string;
                export const UserId: string;
                export const Username: string;
            }

            namespace DebitNoteDetails {
                export const AssessmentRemarks: string;
                export const CGSTAmountPerUnit: string;
                export const CGSTRate: string;
                export const CommodityCode: string;
                export const CommodityDescription: string;
                export const CommodityId: string;
                export const CommodityName: string;
                export const CommodityType: string;
                export const CommodityTypeId: string;
                export const DebitNoteDetailId: string;
                export const DebitNoteId: string;
                export const DebitNoteNo: string;
                export const DiscountAmountPerUnit: string;
                export const DiscountPercent: string;
                export const DummyField: string;
                export const GSTRateId: string;
                export const GSTRateRemarks: string;
                export const IGSTAmountPerUnit: string;
                export const IGSTRate: string;
                export const NetAmount: string;
                export const NetCGSTAmount: string;
                export const NetDiscountAmount: string;
                export const NetIGSTAmount: string;
                export const NetPricePerUnit: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const PoQuantity: string;
                export const PoUnitId: string;
                export const PoUnitUnitName: string;
                export const PurchaseOrderDetailCommodityDescription: string;
                export const PurchaseOrderDetailId: string;
                export const PurchaseReturnDetailCommodityDescription: string;
                export const PurchaseReturnDetailId: string;
                export const RejectionReason: string;
                export const RejectionReasonId: string;
                export const Remarks: string;
                export const ReplacementMethod: string;
                export const ReplacementMethodId: string;
                export const ReturnedQuantity: string;
                export const ReturnedUnitId: string;
                export const ReturnedUnitUnitName: string;
                export const RowNumber: string;
                export const SGSTAmountPerUnit: string;
                export const SGSTRate: string;
                export const SerialNos: string;
                export const TaxableAmountPerUnit: string;
                export const UnitAmount: string;
                export const UnitPrice: string;
            }

            namespace DebitNotes {
                export const AuthorizedByUserId: string;
                export const AuthorizedByUserUsername: string;
                export const AuthorizedDate: string;
                export const AuthorizedStatus: string;
                export const CancelledByUserId: string;
                export const CancelledByUserUsername: string;
                export const CancelledDate: string;
                export const ClientId: string;
                export const ClientName: string;
                export const DebitNoteAmount: string;
                export const DebitNoteDate: string;
                export const DebitNoteDetailsList: string;
                export const DebitNoteId: string;
                export const DebitNoteMonth: string;
                export const DebitNoteNo: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const ModifiedByUserId: string;
                export const ModifiedByUserUsername: string;
                export const ModifiedDate: string;
                export const NetCGSTAmount: string;
                export const NetIGSTAmount: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const PlaceOfSupplyStateName: string;
                export const PreparedByUserId: string;
                export const PreparedByUserUsername: string;
                export const PreparedDate: string;
                export const PurchaseOrderId: string;
                export const PurchaseOrderNo: string;
                export const PurchaseReturnId: string;
                export const PurchaseReturnNo: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const VendorId: string;
                export const VendorName: string;
                export const VerifiedByUserId: string;
                export const VerifiedByUserUsername: string;
                export const VerifiedDate: string;
            }

            namespace DeliveryNoteDetails {
                export const CGSTAmount: string;
                export const CGSTRate: string;
                export const CommodityCode: string;
                export const CommodityDescription: string;
                export const CommodityId: string;
                export const CommodityName: string;
                export const CommodityType: string;
                export const CommodityTypeId: string;
                export const DeliveryNoteDetailId: string;
                export const DeliveryNoteId: string;
                export const DeliveryNoteNo: string;
                export const DummyField: string;
                export const DummyField1: string;
                export const GSTRateId: string;
                export const GSTRateRemarks: string;
                export const HSNSACCode: string;
                export const HSNSACCodeId: string;
                export const HSNSACDescription: string;
                export const HSNSACGroup: string;
                export const IGSTAmount: string;
                export const IGSTRate: string;
                export const NetAmount: string;
                export const PerUnitCGSTAmount: string;
                export const PerUnitIGSTAmount: string;
                export const PerUnitPrice: string;
                export const PerUnitSGSTAmount: string;
                export const Quantity: string;
                export const RowNumber: string;
                export const SGSTAmount: string;
                export const SGSTRate: string;
                export const Sku: string;
                export const UnitAmount: string;
                export const UnitId: string;
                export const UnitName: string;
                export const UnitPrice: string;
            }

            namespace DeliveryNotes {
                export const AuthorizedByUserId: string;
                export const AuthorizedByUserUsername: string;
                export const AuthorizedDate: string;
                export const AuthorizedStatus: string;
                export const BillingAddress: string;
                export const BillingCityCityName: string;
                export const BillingPinCode: string;
                export const CGSTAmount: string;
                export const CancelledByUserId: string;
                export const CancelledByUserUsername: string;
                export const CancelledDate: string;
                export const ClientId: string;
                export const ClientName: string;
                export const CustomerCompanyName: string;
                export const CustomerEMailId: string;
                export const CustomerId: string;
                export const DeliveryNoteDate: string;
                export const DeliveryNoteDetailsList: string;
                export const DeliveryNoteId: string;
                export const DeliveryNoteMonth: string;
                export const DeliveryNoteNo: string;
                export const EWayBillNo: string;
                export const EWayBillNoDate: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const GSTIN: string;
                export const IGSTAmount: string;
                export const Inspection: string;
                export const ModifiedByUserId: string;
                export const ModifiedByUserUsername: string;
                export const ModifiedDate: string;
                export const OrderRefDate: string;
                export const OrderRefNo: string;
                export const PlaceOfSupplyStateName: string;
                export const PreparedByUserId: string;
                export const PreparedByUserUsername: string;
                export const PreparedDate: string;
                export const QuotationId: string;
                export const QuotationNo: string;
                export const ReasonToTransport: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const SGSTAmount: string;
                export const SalesOrderId: string;
                export const SalesOrderNo: string;
                export const SalesOrderRefDate: string;
                export const SalesOrderRefNo: string;
                export const ShipToCustomerId: string;
                export const ShipToCustomerName: string;
                export const ShippedVia: string;
                export const ShippingAddress: string;
                export const ShippingCityName: string;
                export const ShippingDocketNo: string;
                export const ShippingGSTIN: string;
                export const ShippingPinCode: string;
                export const ShippingPlaceOfSupplyStateName: string;
                export const SupplyType: string;
                export const SupplyTypeId: string;
                export const UploadDocketCopy: string;
                export const VehicleNo: string;
                export const VerifiedByUserId: string;
                export const VerifiedByUserUsername: string;
                export const VerifiedDate: string;
            }

            namespace Departments {
                export const DepartmentId: string;
                export const DepartmentName: string;
                export const Description: string;
                export const RowNumber: string;
            }

            namespace Designations {
                export const Description: string;
                export const Designation: string;
                export const DesignationId: string;
                export const RowNumber: string;
            }

            namespace Districts {
                export const District: string;
                export const DistrictCode: string;
                export const DistrictId: string;
                export const Headquarters: string;
                export const RowNumber: string;
                export const StateId: string;
                export const StateName: string;
            }

            namespace Documents {
                export const DocumentId: string;
                export const DocumentName: string;
                export const DocumentShortName: string;
                export const RowNumber: string;
            }

            namespace Employees {
                export const Address: string;
                export const AlternateNo: string;
                export const CityId: string;
                export const CityName: string;
                export const ClientId: string;
                export const ClientName: string;
                export const ClientsConsultantName: string;
                export const ConsultantId: string;
                export const ConsultantName: string;
                export const DateOfBirth: string;
                export const EmailId: string;
                export const EmployeeId: string;
                export const EmployeeName: string;
                export const HireDate: string;
                export const MobileNo: string;
                export const Notes: string;
                export const PhoneNo: string;
                export const PostalCode: string;
                export const RowNumber: string;
                export const UploadDocuments: string;
                export const UserTypeId: string;
            }

            namespace Feedbacks {
                export const ClientId: string;
                export const ClientName: string;
                export const Description: string;
                export const Feedback: string;
                export const FeedbackId: string;
                export const RowNumber: string;
            }

            namespace FinancialYears {
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const FromDate: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const ToDate: string;
            }

            namespace FootNotes {
                export const ClientId: string;
                export const ClientName: string;
                export const FootNote: string;
                export const FootNoteId: string;
                export const Remarks: string;
                export const RowNumber: string;
            }

            namespace GrNs {
                export const AuthorizedByUserId: string;
                export const AuthorizedByUserUsername: string;
                export const AuthorizedDate: string;
                export const AuthorizedStatus: string;
                export const CancelledByUserId: string;
                export const CancelledByUserUsername: string;
                export const CancelledDate: string;
                export const ClientId: string;
                export const ClientName: string;
                export const DeliveryAddress: string;
                export const DeliveryCityCityName: string;
                export const DeliveryCityId: string;
                export const DeliveryPinCode: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const GRNDate: string;
                export const GRNId: string;
                export const GRNMonth: string;
                export const GRNNo: string;
                export const GRNTypeId: string;
                export const GRNTypeName: string;
                export const GSTIN: string;
                export const GatePassDate: string;
                export const GatePassNo: string;
                export const GrnDetailsList: string;
                export const Inspection: string;
                export const ModifiedByUserId: string;
                export const ModifiedByUserUsername: string;
                export const ModifiedDate: string;
                export const PreparedByUserId: string;
                export const PreparedByUserUsername: string;
                export const PreparedDate: string;
                export const PurchaseOrderId: string;
                export const PurchaseOrderNo: string;
                export const ReceivedByEmployeeEmployeeName: string;
                export const ReceivedByEmployeeId: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const ShippedThrough: string;
                export const ShippingDocketNo: string;
                export const UploadDocuments: string;
                export const VehicleNo: string;
                export const VehicleType: string;
                export const VendorDcInvoiceDate: string;
                export const VendorDcInvoiceNo: string;
                export const VendorEMailId: string;
                export const VendorId: string;
                export const VendorName: string;
                export const VerifiedByUserId: string;
                export const VerifiedByUserUsername: string;
                export const VerifiedDate: string;
            }

            namespace GrnDetails {
                export const AcceptedQuantity: string;
                export const AcceptedUnitId: string;
                export const AcceptedUnitUnitName: string;
                export const ClientId: string;
                export const ClientName: string;
                export const CommodityCode: string;
                export const CommodityDescription: string;
                export const CommodityId: string;
                export const CommodityName: string;
                export const CommodityType: string;
                export const CommodityTypeId: string;
                export const GRNId: string;
                export const GRNNo: string;
                export const GrnDetailId: string;
                export const LocationId: string;
                export const LocationName: string;
                export const PoQuantity: string;
                export const PoUnitId: string;
                export const PoUnitUnitName: string;
                export const PurchaseOrderDetailCommodityDescription: string;
                export const PurchaseOrderDetailId: string;
                export const RackId: string;
                export const RackNo: string;
                export const ReceivedQuantity: string;
                export const ReceivedUnitId: string;
                export const ReceivedUnitUnitName: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const SerialNos: string;
                export const Sku: string;
                export const StoreId: string;
                export const StoreName: string;
                export const SupplyDueDate: string;
                export const WarehouseId: string;
                export const WarehouseName: string;
            }

            namespace GrnTypes {
                export const Description: string;
                export const GRNTypeId: string;
                export const GRNTypeName: string;
                export const RowNumber: string;
            }

            namespace GstRates {
                export const CGSTCessPercent: string;
                export const CGSTPercent: string;
                export const Current: string;
                export const GSTRateId: string;
                export const IGSTCessPercent: string;
                export const IGSTPercent: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const SGSTCessPercent: string;
                export const SGSTPercent: string;
                export const WefDate: string;
            }

            namespace HeaderNote {
                export const ClientId: string;
                export const HeaderNote: string;
                export const HeaderNoteId: string;
                export const Remarks: string;
                export const RowNumber: string;
            }

            namespace HsnSummary {
                export const Cess: string;
                export const Cgst: string;
                export const Description: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const HsnCode: string;
                export const HsnDescription: string;
                export const HsnSummaryId: string;
                export const Igst: string;
                export const InvoiceDetailId: string;
                export const InvoiceId: string;
                export const InvoiceMonth: string;
                export const Invoices: string;
                export const NetAmount: string;
                export const Quantity: string;
                export const Remarks: string;
                export const Sgst: string;
                export const TaxableValue: string;
                export const UQC: string;
            }

            namespace HsnSummaryDetails {
                export const CGSTRate: string;
                export const HSNSACCode: string;
                export const HsnSummaryDetailId: string;
                export const IGSTRate: string;
                export const InvoiceDate: string;
                export const InvoiceNo: string;
                export const NetAmount: string;
                export const NetCGSTAmount: string;
                export const NetIGSTAmount: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const Quantity: string;
                export const RowNumber: string;
                export const SGSTRate: string;
            }

            namespace HsnsacCodes {
                export const GSTRateId: string;
                export const GSTRateRemarks: string;
                export const HSNSACCode: string;
                export const HSNSACCodeId: string;
                export const HSNSACDescription: string;
                export const HSNSACGroup: string;
                export const RowNumber: string;
            }

            namespace InvoiceDetails {
                export const CGSTAmountPerUnit: string;
                export const CGSTRate: string;
                export const ClientId: string;
                export const CommodityCode: string;
                export const CommodityDescription: string;
                export const CommodityId: string;
                export const CommodityName: string;
                export const CommodityType: string;
                export const CommodityTypeId: string;
                export const DiscountAmountPerUnit: string;
                export const DiscountPercent: string;
                export const DummyField: string;
                export const FinancialYearId: string;
                export const GSTRateId: string;
                export const GSTRateRemarks: string;
                export const HSNSACCode: string;
                export const HSNSACCodeId: string;
                export const HSNSACDescription: string;
                export const HSNSACGroup: string;
                export const IGSTAmountPerUnit: string;
                export const IGSTRate: string;
                export const InvoiceDate: string;
                export const InvoiceDetailId: string;
                export const InvoiceId: string;
                export const InvoiceMonth: string;
                export const InvoiceNo: string;
                export const NetAmount: string;
                export const NetCGSTAmount: string;
                export const NetDiscountAmount: string;
                export const NetIGSTAmount: string;
                export const NetPricePerUnit: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const NetUnitAmount: string;
                export const ProductSerialNos: string;
                export const Quantity: string;
                export const RevisedQuantity: string;
                export const RowNumber: string;
                export const SGSTAmountPerUnit: string;
                export const SGSTRate: string;
                export const SKU: string;
                export const TaxableAmountPerUnit: string;
                export const UnitId: string;
                export const UnitName: string;
                export const UnitPrice: string;
            }

            namespace Invoices {
                export const AcknowledgementDate: string;
                export const AcknowledgementNo: string;
                export const AuthorizedByUserId: string;
                export const AuthorizedByUserUsername: string;
                export const AuthorizedDate: string;
                export const AuthorizedStatus: string;
                export const BillingAddress: string;
                export const BillingCityCityName: string;
                export const BillingPinCode: string;
                export const CancelReason: string;
                export const CancelledByUserId: string;
                export const CancelledByUserUsername: string;
                export const CancelledDate: string;
                export const ClientId: string;
                export const ClientName: string;
                export const CustomerCompanyName: string;
                export const CustomerEMailId: string;
                export const CustomerId: string;
                export const DeliveryNoteId: string;
                export const DeliveryNoteNo: string;
                export const EInvoiceQRCode: string;
                export const EWayBillDate: string;
                export const EWayBillNo: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const GSTIN: string;
                export const GrandTotal: string;
                export const IRN: string;
                export const Inspection: string;
                export const InvoiceAmount: string;
                export const InvoiceDate: string;
                export const InvoiceDetailsList: string;
                export const InvoiceId: string;
                export const InvoiceMonth: string;
                export const InvoiceNo: string;
                export const ModifiedByUserId: string;
                export const ModifiedByUserUsername: string;
                export const ModifiedDate: string;
                export const NetCGSTAmount: string;
                export const NetIGSTAmount: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const OrderRefDate: string;
                export const OrderRefNo: string;
                export const PaymentDueDate: string;
                export const PaymentTerms: string;
                export const PaymentTermsId: string;
                export const PlaceOfSupplyStateName: string;
                export const PreparedByUserId: string;
                export const PreparedByUserUsername: string;
                export const PreparedDate: string;
                export const ProformaInvoiceId: string;
                export const ProformaInvoiceNo: string;
                export const Remarks: string;
                export const RoundingOff: string;
                export const RowNumber: string;
                export const SalesOrderId: string;
                export const SalesOrderNo: string;
                export const SalesPersonEmployeeName: string;
                export const SalesPersonId: string;
                export const ShipToCustomerId: string;
                export const ShipToCustomerName: string;
                export const ShippedVia: string;
                export const ShippingAddress: string;
                export const ShippingCityName: string;
                export const ShippingDocketNo: string;
                export const ShippingGSTIN: string;
                export const ShippingPinCode: string;
                export const ShippingPlaceOfSupplyStateName: string;
                export const SupplyType: string;
                export const SupplyTypeId: string;
                export const TransactionStatus: string;
                export const UploadFiles: string;
                export const VehicleNo: string;
                export const VerifiedByUserId: string;
                export const VerifiedByUserUsername: string;
                export const VerifiedDate: string;
            }

            namespace LedgerAccounts {
                export const LedgerAccountId: string;
                export const LedgerAccountName: string;
                export const LedgerCreationDate: string;
                export const RowNumber: string;
                export const SecondaryGroupId: string;
                export const SecondaryGroupName: string;
            }

            namespace Locations {
                export const ClientId: string;
                export const ClientName: string;
                export const Description: string;
                export const Discontinued: string;
                export const LocationId: string;
                export const LocationName: string;
                export const Remarks: string;
                export const RowNumber: string;
            }

            namespace ModeOfPayments {
                export const Description: string;
                export const ModeOfPayment: string;
                export const ModeOfPaymentId: string;
                export const RowNumber: string;
            }

            namespace Narrations {
                export const ClientId: string;
                export const ClientName: string;
                export const NarrationId: string;
                export const NarrationText: string;
                export const Remarks: string;
                export const RowNumber: string;
            }

            namespace NatureOfSupply {
                export const Description: string;
                export const NatureOfSupply: string;
                export const NatureOfSupplyId: string;
                export const RowNumber: string;
            }

            namespace OpeningBalances {
                export const Ammount: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const FromDate: string;
                export const LedgerAccountId: string;
                export const OpeningBalanceId: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const ToDate: string;
            }

            namespace PaymentTerms {
                export const PaymentTerms: string;
                export const PaymentTermsId: string;
                export const RowNumber: string;
            }

            namespace PoAmendmentDetails {
                export const AmendedQuantity: string;
                export const AmendedUnitAmount: string;
                export const AmendedUnitId: string;
                export const AmendedUnitPrice: string;
                export const AmendedUnitUnitName: string;
                export const CGSTAmountPerUnit: string;
                export const CGSTRate: string;
                export const CommodityCode: string;
                export const CommodityDescription: string;
                export const CommodityId: string;
                export const CommodityName: string;
                export const CommodityType: string;
                export const CommodityTypeId: string;
                export const DummyField: string;
                export const GSTRateId: string;
                export const GSTRateRemarks: string;
                export const HSNSACCode: string;
                export const HSNSACCodeId: string;
                export const HSNSACDescription: string;
                export const HSNSACGroup: string;
                export const IGSTAmountPerUnit: string;
                export const IGSTRate: string;
                export const NetAmount: string;
                export const NetCGSTAmount: string;
                export const NetIGSTAmount: string;
                export const NetPricePerUnit: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const POAmendmentDetailId: string;
                export const POAmendmentId: string;
                export const POAmendmentNo: string;
                export const POQuantity: string;
                export const POUnitAmount: string;
                export const POUnitId: string;
                export const POUnitPrice: string;
                export const POUnitUnitName: string;
                export const PendingQuantity: string;
                export const RowNumber: string;
                export const SGSTAmountPerUnit: string;
                export const SGSTRate: string;
                export const TaxableAmountPerUnit: string;
            }

            namespace PoAmendments {
                export const AuthorizedByUserId: string;
                export const AuthorizedByUserUsername: string;
                export const AuthorizedDate: string;
                export const AuthorizedStatus: string;
                export const CancelledByUserId: string;
                export const CancelledByUserUsername: string;
                export const CancelledDate: string;
                export const ClientId: string;
                export const ClientName: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const GSTIN: string;
                export const GrandTotal: string;
                export const ModifiedByUserId: string;
                export const ModifiedByUserUsername: string;
                export const ModifiedDate: string;
                export const NetCGSTAmount: string;
                export const NetIGSTAmount: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const POAmendmentAmount: string;
                export const POAmendmentDate: string;
                export const POAmendmentId: string;
                export const POAmendmentMonth: string;
                export const POAmendmentNo: string;
                export const PlaceOfSupplyStateName: string;
                export const PoAmendmentDetailsList: string;
                export const PreparedByUserId: string;
                export const PreparedByUserUsername: string;
                export const PreparedDate: string;
                export const PurchaseOrderDetailCommodityDescription: string;
                export const PurchaseOrderDetailId: string;
                export const PurchaseOrderId: string;
                export const PurchaseOrderNo: string;
                export const Remarks: string;
                export const RoundingOff: string;
                export const RowNumber: string;
                export const TCSRateId: string;
                export const TCSRateNatureOfTransaction: string;
                export const TDSRateId: string;
                export const TDSRateTransaction: string;
                export const VendorId: string;
                export const VendorName: string;
                export const VerifiedByUserId: string;
                export const VerifiedByUserUsername: string;
                export const VerifiedDate: string;
            }

            namespace PrimaryGroups {
                export const PrimaryGroupCode: string;
                export const PrimaryGroupId: string;
                export const PrimaryGroupName: string;
                export const Remarks: string;
                export const RowNumber: string;
            }

            namespace ProductCategories {
                export const CategoryName: string;
                export const ClientId: string;
                export const ClientName: string;
                export const Description: string;
                export const ProductCategoryId: string;
                export const RowNumber: string;
            }

            namespace ProductGroups {
                export const ClientId: string;
                export const ClientName: string;
                export const ProductGroup: string;
                export const ProductGroupId: string;
                export const RowNumber: string;
            }

            namespace ProductMake {
                export const ClientId: string;
                export const ClientName: string;
                export const ProductMake: string;
                export const ProductMakeId: string;
                export const RowNumber: string;
            }

            namespace ProductTypes {
                export const ClientId: string;
                export const ClientName: string;
                export const ProductType: string;
                export const ProductTypeId: string;
                export const RowNumber: string;
            }

            namespace ProformaInvoiceDetails {
                export const CGSTAmountPerUnit: string;
                export const CGSTRate: string;
                export const CommodityCode: string;
                export const CommodityDescription: string;
                export const CommodityId: string;
                export const CommodityName: string;
                export const CommodityType: string;
                export const CommodityTypeId: string;
                export const DiscountAmountPerUnit: string;
                export const DiscountPercent: string;
                export const DummyField: string;
                export const GSTRateId: string;
                export const GSTRateRemarks: string;
                export const HSNSACCode: string;
                export const HSNSACCodeId: string;
                export const HSNSACDescription: string;
                export const HSNSACGroup: string;
                export const IGSTAmountPerUnit: string;
                export const IGSTRate: string;
                export const NetAmount: string;
                export const NetCGSTAmount: string;
                export const NetDiscountAmount: string;
                export const NetIGSTAmount: string;
                export const NetPricePerUnit: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const ProformaInvoiceDetailId: string;
                export const ProformaInvoiceId: string;
                export const ProformaInvoiceNo: string;
                export const Quantity: string;
                export const RowNumber: string;
                export const SGSTAmountPerUnit: string;
                export const SGSTRate: string;
                export const TaxableAmountPerUnit: string;
                export const UnitAmount: string;
                export const UnitId: string;
                export const UnitName: string;
                export const UnitPrice: string;
            }

            namespace ProformaInvoices {
                export const AuthorizedByUserId: string;
                export const AuthorizedByUserUsername: string;
                export const AuthorizedDate: string;
                export const AuthorizedStatus: string;
                export const BillingAddress: string;
                export const BillingCityCityName: string;
                export const BillingPinCode: string;
                export const CancelledByUserId: string;
                export const CancelledByUserUsername: string;
                export const CancelledDate: string;
                export const ClientId: string;
                export const ClientName: string;
                export const CustomerCompanyName: string;
                export const CustomerEMailId: string;
                export const CustomerId: string;
                export const DeliveryNoteId: string;
                export const DeliveryNoteNo: string;
                export const DocketNo: string;
                export const DummyField: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const GSTIN: string;
                export const GrandTotal: string;
                export const Inspection: string;
                export const ModifiedByUserId: string;
                export const ModifiedByUserUsername: string;
                export const ModifiedDate: string;
                export const NetCGSTAmount: string;
                export const NetIGSTAmount: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const OrderRefDate: string;
                export const OrderRefNo: string;
                export const PaymentDueDate: string;
                export const PaymentTerms: string;
                export const PaymentTermsId: string;
                export const PlaceOfSupplyStateName: string;
                export const PreparedByUserId: string;
                export const PreparedByUserUsername: string;
                export const PreparedDate: string;
                export const ProformaInvoiceAmt: string;
                export const ProformaInvoiceDate: string;
                export const ProformaInvoiceDetailsList: string;
                export const ProformaInvoiceId: string;
                export const ProformaInvoiceMonth: string;
                export const ProformaInvoiceNo: string;
                export const QuotationId: string;
                export const QuotationNo: string;
                export const Remarks: string;
                export const RoundingOff: string;
                export const RowNumber: string;
                export const SalesOrderId: string;
                export const SalesOrderNo: string;
                export const ShipToCustomerId: string;
                export const ShipToCustomerName: string;
                export const ShippedVia: string;
                export const ShippingAddress: string;
                export const ShippingCityName: string;
                export const ShippingGSTIN: string;
                export const ShippingPinCode: string;
                export const ShippingPlaceOfSupplyStateName: string;
                export const SupplyType: string;
                export const SupplyTypeId: string;
                export const UploadFiles: string;
                export const VehicleNo: string;
                export const VerifiedByUserId: string;
                export const VerifiedByUserUsername: string;
                export const VerifiedDate: string;
            }

            namespace PurchaseOrderDetails {
                export const AmendedPrice: string;
                export const AmendedQuantity: string;
                export const AmendedUnitId: string;
                export const CGSTAmountPerUnit: string;
                export const CGSTRate: string;
                export const CommodityCode: string;
                export const CommodityDescription: string;
                export const CommodityId: string;
                export const CommodityName: string;
                export const CommodityType: string;
                export const CommodityTypeId: string;
                export const DiscountAmountPerUnit: string;
                export const DiscountPercent: string;
                export const DummyField: string;
                export const GSTRateId: string;
                export const GSTRateRemarks: string;
                export const HSNSACCode: string;
                export const HSNSACCodeId: string;
                export const HSNSACDescription: string;
                export const HSNSACGroup: string;
                export const IGSTAmountPerUnit: string;
                export const IGSTRate: string;
                export const NetAmount: string;
                export const NetCGSTAmount: string;
                export const NetDiscountAmount: string;
                export const NetIGSTAmount: string;
                export const NetPricePerUnit: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const PendingQuantity: string;
                export const PurchaseOrderDetailId: string;
                export const PurchaseOrderId: string;
                export const PurchaseOrderNo: string;
                export const Quantity: string;
                export const RowNumber: string;
                export const SGSTAmountPerUnit: string;
                export const SGSTRate: string;
                export const TaxableAmountPerUnit: string;
                export const UnitAmount: string;
                export const UnitId: string;
                export const UnitName: string;
                export const UnitPrice: string;
            }

            namespace PurchaseOrders {
                export const AuthorizedByUserId: string;
                export const AuthorizedByUserUsername: string;
                export const AuthorizedDate: string;
                export const AuthorizedStatus: string;
                export const BillingAddress: string;
                export const BillingCityCityName: string;
                export const BillingPinCode: string;
                export const CancelledByUserId: string;
                export const CancelledByUserUsername: string;
                export const CancelledDate: string;
                export const ClientId: string;
                export const ClientName: string;
                export const CustomerCompanyName: string;
                export const DeliveryDueDate: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const FootNote: string;
                export const FootNoteId: string;
                export const GSTIN: string;
                export const GrandTotal: string;
                export const HeaderNote: string;
                export const HeaderNoteId: string;
                export const Inspection: string;
                export const ModifiedByUserId: string;
                export const ModifiedByUserUsername: string;
                export const ModifiedDate: string;
                export const NetCGSTAmount: string;
                export const NetIGSTAmount: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const POAmount: string;
                export const POStatus: string;
                export const PaymentDueDate: string;
                export const PaymentTerms: string;
                export const PaymentTermsId: string;
                export const PlaceOfSupplyStateName: string;
                export const PreparedByUserId: string;
                export const PreparedByUserUsername: string;
                export const PreparedDate: string;
                export const PurchaseOrderDate: string;
                export const PurchaseOrderDetailsList: string;
                export const PurchaseOrderId: string;
                export const PurchaseOrderMonth: string;
                export const PurchaseOrderNo: string;
                export const ReferenceDate: string;
                export const ReferenceNo: string;
                export const Remarks: string;
                export const RoundingOff: string;
                export const RowNumber: string;
                export const ShipToCustomerId: string;
                export const ShipToCustomerName: string;
                export const ShippingAddress: string;
                export const ShippingCityName: string;
                export const ShippingGSTIN: string;
                export const ShippingPinCode: string;
                export const ShippingPlaceOfSupplyStateName: string;
                export const SupplyType: string;
                export const SupplyTypeId: string;
                export const TCSAmount: string;
                export const TCSRateId: string;
                export const TDSAmount: string;
                export const TDSRateId: string;
                export const Taxes: string;
                export const UploadDocuments: string;
                export const VendorEMailId: string;
                export const VendorId: string;
                export const VendorName: string;
                export const VerifiedByUserId: string;
                export const VerifiedByUserUsername: string;
                export const VerifiedDate: string;
            }

            namespace PurchaseReturnDetails {
                export const AssessmentRemarks: string;
                export const CommodityCode: string;
                export const CommodityDescription: string;
                export const CommodityId: string;
                export const CommodityName: string;
                export const CommodityType: string;
                export const CommodityTypeId: string;
                export const GRNDetailCommodityDescription: string;
                export const GRNDetailId: string;
                export const HSNSACCode: string;
                export const HSNSACCodeId: string;
                export const HSNSACDescription: string;
                export const HSNSACGroup: string;
                export const PurchaseOrderDetailCommodityDescription: string;
                export const PurchaseOrderDetailId: string;
                export const PurchaseReturnDetailId: string;
                export const PurchaseReturnId: string;
                export const PurchaseReturnNo: string;
                export const RejectedAmount: string;
                export const RejectedItemSerialNo: string;
                export const RejectedQuantity: string;
                export const RejectionReason: string;
                export const RejectionReasonId: string;
                export const Remarks: string;
                export const ReplacementMethod: string;
                export const ReplacementMethodId: string;
                export const RowNumber: string;
                export const UnitId: string;
                export const UnitName: string;
            }

            namespace PurchaseReturns {
                export const AuthorizedByUserId: string;
                export const AuthorizedByUserUsername: string;
                export const AuthorizedDate: string;
                export const AuthorizedStatus: string;
                export const CancelledByUserId: string;
                export const CancelledByUserUsername: string;
                export const CancelledDate: string;
                export const ClientId: string;
                export const ClientName: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const GRNId: string;
                export const GRNNo: string;
                export const ModifiedByUserId: string;
                export const ModifiedByUserUsername: string;
                export const ModifiedDate: string;
                export const PreparedByUserId: string;
                export const PreparedByUserUsername: string;
                export const PreparedDate: string;
                export const PurchaseOrderId: string;
                export const PurchaseOrderNo: string;
                export const PurchaseReturnDate: string;
                export const PurchaseReturnDetailsList: string;
                export const PurchaseReturnId: string;
                export const PurchaseReturnMonth: string;
                export const PurchaseReturnNo: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const UploadFiles: string;
                export const VendorId: string;
                export const VendorName: string;
                export const VerifiedByUserId: string;
                export const VerifiedByUserUsername: string;
                export const VerifiedDate: string;
            }

            namespace QuotationDetails {
                export const Amount: string;
                export const CGSTRate: string;
                export const CommodityCode: string;
                export const CommodityDescription: string;
                export const CommodityId: string;
                export const CommodityName: string;
                export const CommodityType: string;
                export const CommodityTypeId: string;
                export const DiscountAmount: string;
                export const DiscountAmountPerUnit: string;
                export const DiscountPercent: string;
                export const GSTRateId: string;
                export const GSTRateRemarks: string;
                export const GrossTotal: string;
                export const HSNSACCode: string;
                export const HSNSACCodeId: string;
                export const HSNSACDescription: string;
                export const HSNSACGroup: string;
                export const IGSTRate: string;
                export const NetAmount: string;
                export const NetCGSTAmount: string;
                export const NetIGSTAmount: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const PerUnitCGSTAmount: string;
                export const PerUnitIGSTAmount: string;
                export const PerUnitPrice: string;
                export const PerUnitSGSTAmount: string;
                export const Quantity: string;
                export const QuotationDetailId: string;
                export const QuotationId: string;
                export const QuotationNo: string;
                export const RevisedQuantity: string;
                export const RowNumber: string;
                export const SGSTRate: string;
                export const SalesPrice: string;
                export const TaxableAmountPerUnit: string;
                export const UnitId: string;
                export const UnitName: string;
                export const UnitPrice: string;
            }

            namespace Quotations {
                export const AuthorizedByUserId: string;
                export const AuthorizedByUserUsername: string;
                export const AuthorizedDate: string;
                export const AuthorizedStatus: string;
                export const BillingAddress: string;
                export const BillingCityCityName: string;
                export const BillingPinCode: string;
                export const CancelledByUserId: string;
                export const CancelledByUserUsername: string;
                export const CancelledDate: string;
                export const ClientId: string;
                export const ClientName: string;
                export const CustomerCompanyName: string;
                export const CustomerEMailId: string;
                export const CustomerId: string;
                export const DeliveryPeriod: string;
                export const DocumentId: string;
                export const DocumentName: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const FootNote: string;
                export const FootNoteId: string;
                export const GSTIN: string;
                export const GrandTotal: string;
                export const HeaderNote: string;
                export const HeaderNoteId: string;
                export const ModifiedByUserId: string;
                export const ModifiedByUserUsername: string;
                export const ModifiedDate: string;
                export const NetCGSTAmount: string;
                export const NetIGSTAmount: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const PaymentTerms: string;
                export const PaymentTermsId: string;
                export const PlaceOfSupplyStateName: string;
                export const PreparedByUserId: string;
                export const PreparedByUserUsername: string;
                export const PreparedDate: string;
                export const QuotationDate: string;
                export const QuotationDetailsList: string;
                export const QuotationId: string;
                export const QuotationMonth: string;
                export const QuotationNo: string;
                export const QuotationStatus: string;
                export const ReferenceDate: string;
                export const ReferenceNo: string;
                export const Remarks: string;
                export const RoundingOff: string;
                export const RowNumber: string;
                export const SalesPerson: string;
                export const SalesPersonEmployeeName: string;
                export const Taxes: string;
                export const Validity: string;
                export const VerifiedByUserId: string;
                export const VerifiedByUserUsername: string;
                export const VerifiedDate: string;
                export const Warranty: string;
            }

            namespace Racks {
                export const BinNo: string;
                export const ClientId: string;
                export const ClientName: string;
                export const CompartmentNo: string;
                export const Discontinued: string;
                export const RackDescription: string;
                export const RackId: string;
                export const RackNo: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const StoreId: string;
                export const StoreName: string;
            }

            namespace ReceiptDetails {
                export const AmountReceived: string;
                export const BalanceReceivable: string;
                export const InvoiceDate: string;
                export const InvoiceGrandTotal: string;
                export const InvoiceId: string;
                export const InvoiceNo: string;
                export const IsTCSPaid: string;
                export const IsTDSPaid: string;
                export const ReceiptDetailId: string;
                export const ReceiptId: string;
                export const ReceiptNo: string;
                export const RowNumber: string;
                export const TCSAmount: string;
                export const TCSRate: string;
                export const TCSRateId: string;
                export const TDSAmount: string;
                export const TDSRate: string;
                export const TDSRateId: string;
                export const TaxableAmount: string;
            }

            namespace Receipts {
                export const AmountReceived: string;
                export const AuthorizedByUserId: string;
                export const AuthorizedByUserUsername: string;
                export const AuthorizedDate: string;
                export const AuthorizedStatus: string;
                export const BankBranchName: string;
                export const CancelReason: string;
                export const CancelledByUserId: string;
                export const CancelledByUserUsername: string;
                export const CancelledDate: string;
                export const ChequeDdDate: string;
                export const ChequeDdNo: string;
                export const ClientId: string;
                export const ClientName: string;
                export const CustomerCompanyName: string;
                export const CustomerId: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const ModeOfPayment: string;
                export const ModeOfPaymentId: string;
                export const ModifiedByUserId: string;
                export const ModifiedByUserUsername: string;
                export const ModifiedDate: string;
                export const Narration: string;
                export const OnAccount: string;
                export const PaymentRefNo: string;
                export const PreparedByUserId: string;
                export const PreparedByUserUsername: string;
                export const PreparedDate: string;
                export const ReceiptDate: string;
                export const ReceiptDetailsList: string;
                export const ReceiptId: string;
                export const ReceiptMonth: string;
                export const ReceiptNo: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const TCSRateId: string;
                export const TCSRateNatureOfTransaction: string;
                export const TDSRateId: string;
                export const TDSRateTransaction: string;
                export const TotalReceivable: string;
                export const TransactionStatus: string;
                export const VerifiedByUserId: string;
                export const VerifiedByUserUsername: string;
                export const VerifiedDate: string;
            }

            namespace RejectionReasons {
                export const ClientId: string;
                export const ClientName: string;
                export const Description: string;
                export const RejectionReason: string;
                export const RejectionReasonId: string;
                export const RowNumber: string;
            }

            namespace ReplacementMethods {
                export const ClientId: string;
                export const ClientName: string;
                export const Description: string;
                export const ReplacementMethod: string;
                export const ReplacementMethodId: string;
                export const RowNumber: string;
            }

            namespace ReturnsFilingForms {
                export const Description: string;
                export const DueDate: string;
                export const Filer: string;
                export const Frequency: string;
                export const NatureOfSupply: string;
                export const NatureOfSupplyId: string;
                export const Remarks: string;
                export const ReturnsFilingForm: string;
                export const ReturnsFilingFormId: string;
                export const RowNumber: string;
                export const SupplyType: string;
                export const SupplyTypeId: string;
            }

            namespace SalesOrderDetails {
                export const CGSTAmountPerUnit: string;
                export const CGSTRate: string;
                export const CommodityCode: string;
                export const CommodityDescription: string;
                export const CommodityId: string;
                export const CommodityName: string;
                export const CommodityType: string;
                export const CommodityTypeId: string;
                export const DiscountAmountPerUnit: string;
                export const DiscountPercent: string;
                export const DummyField: string;
                export const GSTRateId: string;
                export const GSTRateRemarks: string;
                export const HSNSACCode: string;
                export const HSNSACCodeId: string;
                export const HSNSACDescription: string;
                export const HSNSACGroup: string;
                export const IGSTAmountPerUnit: string;
                export const IGSTRate: string;
                export const NetAmount: string;
                export const NetCGSTAmount: string;
                export const NetDiscountAmount: string;
                export const NetIGSTAmount: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const OfferPrice: string;
                export const OfferQuantity: string;
                export const OfferUnitId: string;
                export const OfferUnitUnitName: string;
                export const OrderQuantity: string;
                export const OrderUnitAmount: string;
                export const OrderUnitId: string;
                export const OrderUnitPrice: string;
                export const OrderUnitUnitName: string;
                export const PricePerUnit: string;
                export const RowNumber: string;
                export const SGSTAmountPerUnit: string;
                export const SGSTRate: string;
                export const SalesOrderDetailId: string;
                export const SalesOrderId: string;
                export const SalesOrderNo: string;
                export const TaxableAmountPerUnit: string;
            }

            namespace SalesOrders {
                export const AuthorizedByUserId: string;
                export const AuthorizedByUserUsername: string;
                export const AuthorizedDate: string;
                export const AuthorizedStatus: string;
                export const BillingAddress: string;
                export const BillingCityCityName: string;
                export const BillingPinCode: string;
                export const CancelledByUserId: string;
                export const CancelledByUserUsername: string;
                export const CancelledDate: string;
                export const ClientId: string;
                export const ClientName: string;
                export const CustomerCompanyName: string;
                export const CustomerEMailId: string;
                export const CustomerId: string;
                export const DeliveryDueDate: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const GSTIN: string;
                export const GrandTotal: string;
                export const Inspection: string;
                export const ModifiedByUserId: string;
                export const ModifiedByUserUsername: string;
                export const ModifiedDate: string;
                export const NetCGSTAmount: string;
                export const NetIGSTAmount: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const OrderRefDate: string;
                export const OrderRefNo: string;
                export const PaymentTerms: string;
                export const PaymentTermsId: string;
                export const PlaceOfSupplyStateName: string;
                export const PreparedByUserId: string;
                export const PreparedByUserUsername: string;
                export const PreparedDate: string;
                export const QuotationId: string;
                export const QuotationNo: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const SalesOrderDate: string;
                export const SalesOrderDetailsList: string;
                export const SalesOrderId: string;
                export const SalesOrderMonth: string;
                export const SalesOrderNo: string;
                export const ShipToCustomerId: string;
                export const ShipToCustomerName: string;
                export const ShippingAddress: string;
                export const ShippingCityName: string;
                export const ShippingGSTIN: string;
                export const ShippingPinCode: string;
                export const ShippingPlaceOfSupplyStateName: string;
                export const SupplyType: string;
                export const SupplyTypeId: string;
                export const UploadOrderCopy: string;
                export const VerifiedByUserId: string;
                export const VerifiedByUserUsername: string;
                export const VerifiedDate: string;
            }

            namespace SalesRegister {
                export const Balance: string;
                export const CompanyName: string;
                export const Credit: string;
                export const Date: string;
                export const Debit: string;
                export const DocumentNo: string;
                export const InvoiceId: string;
                export const Narration: string;
                export const ReceiptId: string;
                export const SalesRegisterId: string;
                export const TCSAmount: string;
                export const TDSAmount: string;
            }

            namespace SalesReturnDetails {
                export const AssessmentRemarks: string;
                export const CommodityCode: string;
                export const CommodityDescription: string;
                export const CommodityId: string;
                export const CommodityName: string;
                export const CommodityType: string;
                export const CommodityTypeId: string;
                export const DeliveryNoteDetailId: string;
                export const DeliveryNoteDetailProductDescription: string;
                export const InvoiceDetailCommodityDescription: string;
                export const InvoiceDetailId: string;
                export const InvoiceQuantity: string;
                export const InvoiceUnitId: string;
                export const NetAmount: string;
                export const NetPricePerUnit: string;
                export const NetUnitAmount: string;
                export const RejectedItemSerialNo: string;
                export const RejectedQuantity: string;
                export const RejectedUnitId: string;
                export const RejectionReason: string;
                export const RejectionReasonId: string;
                export const Remarks: string;
                export const ReplacementMethod: string;
                export const ReplacementMethodId: string;
                export const RowNumber: string;
                export const SalesReturnDetailId: string;
                export const SalesReturnId: string;
                export const SalesReturnNo: string;
                export const UnitName: string;
                export const UnitPrice: string;
            }

            namespace SalesReturns {
                export const AuthorizedByUserId: string;
                export const AuthorizedByUserUsername: string;
                export const AuthorizedDate: string;
                export const AuthorizedStatus: string;
                export const BillingAddress: string;
                export const BillingCityCityName: string;
                export const BillingPinCode: string;
                export const CancelledByUserId: string;
                export const CancelledByUserUsername: string;
                export const CancelledDate: string;
                export const ClientId: string;
                export const ClientName: string;
                export const CustomerCompanyName: string;
                export const CustomerId: string;
                export const DeliveryNoteId: string;
                export const DeliveryNoteNo: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const GRNId: string;
                export const GRNNo: string;
                export const GSTIN: string;
                export const InvoiceId: string;
                export const InvoiceNo: string;
                export const ModifiedByUserId: string;
                export const ModifiedByUserUsername: string;
                export const ModifiedDate: string;
                export const PlaceOfSupplyStateName: string;
                export const PreparedByUserId: string;
                export const PreparedByUserUsername: string;
                export const PreparedDate: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const SalesReturnDate: string;
                export const SalesReturnDetailsList: string;
                export const SalesReturnId: string;
                export const SalesReturnMonth: string;
                export const SalesReturnNo: string;
                export const UploadFiles: string;
                export const VerifiedByUserId: string;
                export const VerifiedByUserUsername: string;
                export const VerifiedDate: string;
            }

            namespace SecondaryGroups {
                export const PrimaryGroupId: string;
                export const PrimaryGroupName: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const SecondaryGroupCode: string;
                export const SecondaryGroupId: string;
                export const SecondaryGroupName: string;
            }

            namespace States {
                export const CountryId: string;
                export const CountryName: string;
                export const RowNumber: string;
                export const StateCode: string;
                export const StateCodeNo: string;
                export const StateId: string;
                export const StateName: string;
                export const StateNameWithStateCodeAndNo: string;
            }

            namespace Stores {
                export const ClientId: string;
                export const ClientName: string;
                export const Discontinued: string;
                export const LocationId: string;
                export const LocationName: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const SetDefault: string;
                export const StoreDescription: string;
                export const StoreId: string;
                export const StoreName: string;
                export const WarehouseId: string;
                export const WarehouseName: string;
            }

            namespace SupplyTypes {
                export const NatureOfSupply: string;
                export const NatureOfSupplyId: string;
                export const RowNumber: string;
                export const SetDefault: string;
                export const SupplyType: string;
                export const SupplyTypeId: string;
            }

            namespace TcsRates {
                export const Collectee: string;
                export const Collector: string;
                export const Description: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const IsDefault: string;
                export const NatureOfTransaction: string;
                export const RowNumber: string;
                export const Section: string;
                export const TCSRate: string;
                export const TCSRateId: string;
                export const TCSRateWithSection: string;
                export const WefDate: string;
            }

            namespace TdsRates {
                export const Deductee: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const IsDefault: string;
                export const Limit: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const Section: string;
                export const TDSRate: string;
                export const TDSRateId: string;
                export const TDSRateWithSection: string;
                export const Transaction: string;
                export const WefDate: string;
            }

            namespace Titles {
                export const Gender: string;
                export const RowNumber: string;
                export const TitleId: string;
                export const TitleOfRespect: string;
            }

            namespace Units {
                export const RowNumber: string;
                export const SetDefault: string;
                export const UQCQuantityName: string;
                export const UnitDescription: string;
                export const UnitId: string;
                export const UnitName: string;
                export const UqcId: string;
            }

            namespace UqCs {
                export const QuantityName: string;
                export const QuantityType: string;
                export const RowNumber: string;
                export const UQCDescription: string;
                export const UniqueQuantityCode: string;
                export const UqcId: string;
                export const UqcName: string;
            }

            namespace UserTypes {
                export const Description: string;
                export const UserTypeId: string;
                export const UserTypeName: string;
            }

            namespace VendorBillDetails {
                export const BillQuantity: string;
                export const CGSTAmountPerUnit: string;
                export const CGSTRate: string;
                export const CommodityCode: string;
                export const CommodityDescription: string;
                export const CommodityId: string;
                export const CommodityName: string;
                export const CommodityType: string;
                export const CommodityTypeId: string;
                export const DiscountAmount: string;
                export const DiscountPercent: string;
                export const DummyField: string;
                export const GSTRateId: string;
                export const GSTRateRemarks: string;
                export const HSNSACCode: string;
                export const HSNSACCodeId: string;
                export const HSNSACDescription: string;
                export const HSNSACGroup: string;
                export const IGSTAmountPerUnit: string;
                export const IGSTRate: string;
                export const NetAmount: string;
                export const NetCGSTAmount: string;
                export const NetIGSTAmount: string;
                export const NetPricePerUnit: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const ProductSerialNos: string;
                export const PurchaseOrderDetailCommodityDescription: string;
                export const PurchaseOrderDetailId: string;
                export const RowNumber: string;
                export const SGSTAmountPerUnit: string;
                export const SGSTRate: string;
                export const SKU: string;
                export const TaxableAmountPerUnit: string;
                export const UnitAmount: string;
                export const UnitId: string;
                export const UnitName: string;
                export const UnitPrice: string;
                export const VendorBillDetailId: string;
                export const VendorBillId: string;
            }

            namespace VendorBills {
                export const AuthorizedByUserId: string;
                export const AuthorizedByUserUsername: string;
                export const AuthorizedDate: string;
                export const AuthorizedStatus: string;
                export const CancelledByUserId: string;
                export const CancelledByUserUsername: string;
                export const CancelledDate: string;
                export const ClientId: string;
                export const ClientName: string;
                export const DeliveryDate: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const GSTIN: string;
                export const GrandTotal: string;
                export const ModifiedByUserId: string;
                export const ModifiedByUserUsername: string;
                export const ModifiedDate: string;
                export const NetCGSTAmount: string;
                export const NetIGSTAmount: string;
                export const NetSGSTAmount: string;
                export const NetTaxableAmount: string;
                export const PaymentDueDate: string;
                export const PaymentTerms: string;
                export const PaymentTermsId: string;
                export const PlaceOfSupplyStateName: string;
                export const PreparedByUserId: string;
                export const PreparedByUserUsername: string;
                export const PreparedDate: string;
                export const PurchaseOrderId: string;
                export const PurchaseOrderNo: string;
                export const Remarks: string;
                export const RoundingOff: string;
                export const RowNumber: string;
                export const ShippingDocketNo: string;
                export const ShippingThru: string;
                export const SupplyDueDate: string;
                export const SupplyType: string;
                export const SupplyTypeId: string;
                export const TCSAmount: string;
                export const TCSRate: string;
                export const TDSAmount: string;
                export const TDSRate: string;
                export const VendorBillAmount: string;
                export const VendorBillDate: string;
                export const VendorBillDetailsList: string;
                export const VendorBillId: string;
                export const VendorBillMonth: string;
                export const VendorBillNo: string;
                export const VendorBillStatus: string;
                export const VendorBillUpload: string;
                export const VendorId: string;
                export const VendorName: string;
                export const VerifiedByUserId: string;
                export const VerifiedByUserUsername: string;
                export const VerifiedDate: string;
            }

            namespace VendorContacts {
                export const AlternateNo: string;
                export const ContactName: string;
                export const DepartmentId: string;
                export const DepartmentName: string;
                export const Designation: string;
                export const DesignationId: string;
                export const Email: string;
                export const ExtensionNo: string;
                export const MobileNo: string;
                export const OfficePhoneNo: string;
                export const RowNumber: string;
                export const Status: string;
                export const TitleId: string;
                export const TitleOfRespect: string;
                export const VendorContactId: string;
                export const VendorId: string;
                export const VendorName: string;
            }

            namespace VendorPaymentDetails {
                export const AmountPaid: string;
                export const PurchaseOrderId: string;
                export const PurchaseOrderNo: string;
                export const RowNumber: string;
                export const VendorBillDetailId: string;
                export const VendorBillId: string;
                export const VendorBillNo: string;
                export const VendorPaymentDetailId: string;
                export const VendorPaymentId: string;
                export const VendorPaymentPaymentVoucherNo: string;
            }

            namespace VendorPayments {
                export const AmountPaid: string;
                export const AuthorizedByUserId: string;
                export const AuthorizedByUserUsername: string;
                export const AuthorizedDate: string;
                export const AuthorizedStatus: string;
                export const BankBranchName: string;
                export const CancelledByUserId: string;
                export const CancelledByUserUsername: string;
                export const CancelledDate: string;
                export const ChequeDdDate: string;
                export const ChequeDdNo: string;
                export const ClientId: string;
                export const ClientName: string;
                export const FinancialYearId: string;
                export const FinancialYearName: string;
                export const ModeOfPayment: string;
                export const ModeOfPaymentId: string;
                export const ModifiedByUserId: string;
                export const ModifiedByUserUsername: string;
                export const ModifiedDate: string;
                export const Narration: string;
                export const NetPayable: string;
                export const OnAccount: string;
                export const PaymentRefNo: string;
                export const PaymentVoucherDate: string;
                export const PaymentVoucherMonth: string;
                export const PaymentVoucherNo: string;
                export const PreparedByUserId: string;
                export const PreparedByUserUsername: string;
                export const PreparedDate: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const TCSAmount: string;
                export const TCSRateId: string;
                export const TCSRateNatureOfTransaction: string;
                export const TDSAmount: string;
                export const TDSRateId: string;
                export const TDSRateTransaction: string;
                export const TotalPayable: string;
                export const VendorId: string;
                export const VendorName: string;
                export const VendorPaymentDetailsList: string;
                export const VendorPaymentId: string;
                export const VerifiedByUserId: string;
                export const VerifiedByUserUsername: string;
                export const VerifiedDate: string;
            }

            namespace Vendors {
                export const AccountName: string;
                export const AccountNumber: string;
                export const AddressedTo: string;
                export const AddressedToId: string;
                export const BankId: string;
                export const BankName: string;
                export const BillingAddress: string;
                export const BillingCityCityName: string;
                export const BillingCityId: string;
                export const BillingPinCode: string;
                export const BranchCode: string;
                export const BranchName: string;
                export const CINNo: string;
                export const ClientId: string;
                export const ClientName: string;
                export const CorrespondenceAddress: string;
                export const CorrespondenceCityCityName: string;
                export const CorrespondenceCityId: string;
                export const CorrespondencePinCode: string;
                export const EMailId: string;
                export const FaxNo: string;
                export const GSTIN: string;
                export const HomePage: string;
                export const IECNo: string;
                export const IFSCCode: string;
                export const MobileNo: string;
                export const NatureOfSupply: string;
                export const NatureOfSupplyId: string;
                export const PAN: string;
                export const PhoneNo: string;
                export const PlaceOfSupplyId: string;
                export const PlaceOfSupplyStateCode: string;
                export const PlaceOfSupplyStateCodeNo: string;
                export const PlaceOfSupplyStateName: string;
                export const RowNumber: string;
                export const ShortName: string;
                export const SupplyType: string;
                export const SupplyTypeId: string;
                export const TAN: string;
                export const UdyamNo: string;
                export const UploadFiles: string;
                export const VendorContactsList: string;
                export const VendorId: string;
                export const VendorName: string;
            }

            namespace Warehouses {
                export const ClientId: string;
                export const ClientName: string;
                export const Description: string;
                export const Discontinued: string;
                export const LocationId: string;
                export const LocationName: string;
                export const Remarks: string;
                export const RowNumber: string;
                export const WarehouseId: string;
                export const WarehouseName: string;
            }
        }
    }

    export declare namespace Forms {

        namespace Membership {

            namespace Login {
                export const ForgotPassword: string;
                export const LoginToYourAccount: string;
                export const OR: string;
                export const RememberMe: string;
                export const SignInButton: string;
                export const SignInWithGeneric: string;
                export const SignUpButton: string;
            }

            namespace SendActivation {
                export const FormInfo: string;
                export const FormTitle: string;
                export const SubmitButton: string;
                export const Success: string;
            }

            namespace SignUp {
                export const ActivateEmailSubject: string;
                export const ActivationCompleteMessage: string;
                export const ConfirmDetails: string;
                export const ConfirmEmail: string;
                export const ConfirmPassword: string;
                export const DisplayName: string;
                export const Email: string;
                export const FormInfo: string;
                export const FormTitle: string;
                export const Password: string;
                export const SubmitButton: string;
                export const Success: string;
            }
        }
        export const SiteTitle: string;
    }

    export declare namespace Site {

        namespace AccessDenied {
            export const ClickToChangeUser: string;
            export const ClickToLogin: string;
            export const LackPermissions: string;
            export const NotLoggedIn: string;
            export const PageTitle: string;
        }

        namespace Layout {
            export const Language: string;
            export const Theme: string;
        }

        namespace RolePermissionDialog {
            export const DialogTitle: string;
            export const EditButton: string;
            export const SaveSuccess: string;
        }

        namespace UserDialog {
            export const EditPermissionsButton: string;
            export const EditRolesButton: string;
        }

        namespace UserPermissionDialog {
            export const DialogTitle: string;
            export const Grant: string;
            export const Permission: string;
            export const Revoke: string;
            export const SaveSuccess: string;
        }

        namespace ValidationError {
            export const Title: string;
        }
    }

    export declare namespace Validation {
        export const AuthenticationError: string;
        export const CurrentPasswordMismatch: string;
        export const DeleteForeignKeyError: string;
        export const EmailConfirm: string;
        export const EmailInUse: string;
        export const InvalidActivateToken: string;
        export const InvalidResetToken: string;
        export const MinRequiredPasswordLength: string;
        export const PasswordConfirmMismatch: string;
        export const SavePrimaryKeyError: string;
    }

}

export const Texts: typeof texts = proxyTexts({}, '', {
    Db: {
        Administration: {
            Language: {},
            Role: {},
            RolePermission: {},
            User: {},
            UserPermission: {},
            UserRole: {}
        },
        Default: {
            AddressedTo: {},
            Banks: {},
            BusinessCategories: {},
            BusinessGroups: {},
            BusinessTypes: {},
            Cities: {},
            ClientBankAccounts: {},
            ClientUsers: {},
            Clients: {},
            Commodities: {},
            CommodityTypes: {},
            ConsultantBankAccounts: {},
            Consultants: {},
            Countries: {},
            CreditNoteDetails: {},
            CreditNotes: {},
            CustomerContacts: {},
            CustomerDetails: {},
            CustomerRepresentatives: {},
            Customers: {},
            DataAuditLog: {},
            DebitNoteDetails: {},
            DebitNotes: {},
            DeliveryNoteDetails: {},
            DeliveryNotes: {},
            Departments: {},
            Designations: {},
            Districts: {},
            Documents: {},
            Employees: {},
            Feedbacks: {},
            FinancialYears: {},
            FootNotes: {},
            GrNs: {},
            GrnDetails: {},
            GrnTypes: {},
            GstRates: {},
            HeaderNote: {},
            HsnSummary: {},
            HsnSummaryDetails: {},
            HsnsacCodes: {},
            InvoiceDetails: {},
            Invoices: {},
            LedgerAccounts: {},
            Locations: {},
            ModeOfPayments: {},
            Narrations: {},
            NatureOfSupply: {},
            OpeningBalances: {},
            PaymentTerms: {},
            PoAmendmentDetails: {},
            PoAmendments: {},
            PrimaryGroups: {},
            ProductCategories: {},
            ProductGroups: {},
            ProductMake: {},
            ProductTypes: {},
            ProformaInvoiceDetails: {},
            ProformaInvoices: {},
            PurchaseOrderDetails: {},
            PurchaseOrders: {},
            PurchaseReturnDetails: {},
            PurchaseReturns: {},
            QuotationDetails: {},
            Quotations: {},
            Racks: {},
            ReceiptDetails: {},
            Receipts: {},
            RejectionReasons: {},
            ReplacementMethods: {},
            ReturnsFilingForms: {},
            SalesOrderDetails: {},
            SalesOrders: {},
            SalesRegister: {},
            SalesReturnDetails: {},
            SalesReturns: {},
            SecondaryGroups: {},
            States: {},
            Stores: {},
            SupplyTypes: {},
            TcsRates: {},
            TdsRates: {},
            Titles: {},
            Units: {},
            UqCs: {},
            UserTypes: {},
            VendorBillDetails: {},
            VendorBills: {},
            VendorContacts: {},
            VendorPaymentDetails: {},
            VendorPayments: {},
            Vendors: {},
            Warehouses: {}
        }
    },
    Forms: {
        Membership: {
            Login: {},
            SendActivation: {},
            SignUp: {}
        }
    },
    Site: {
        AccessDenied: {},
        Layout: {},
        RolePermissionDialog: {},
        UserDialog: {},
        UserPermissionDialog: {},
        ValidationError: {}
    },
    Validation: {}
}) as any;